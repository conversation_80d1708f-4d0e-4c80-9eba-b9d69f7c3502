const express = require('express');
const router = express.Router();
const AuthMiddleWare = require('../middleware/auth');
const AclMiddleWare = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const userProfileController = require('../services/userProfile/userProfileController');
const path = require('path');

router.get('/details', AuthMiddleWare, AclMiddleWare, userProfileController.getUserDetails);
router.get('/import-csv-page', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/import-csv.html'));
});
router.post(
  '/import-csv',
  AuthMiddleWare,
  AclMiddleWare,
  UploadMiddleWare.single('file'),
  userProfileController.importFromCSV
);
router.get('/export-csv', AuthMiddleWare, AclMiddleWare, userProfileController.exportToCSV);
router.get('/all-user-details', AuthMiddleWare, AclMiddleWare, userProfileController.getAllUserDetails);


module.exports = router;
