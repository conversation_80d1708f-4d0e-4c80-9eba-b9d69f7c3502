const _ = require('lodash');
const { Op } = require('sequelize');
const User = require('../../../models').user;
const Player = require('../../../models').player;
const PlayerTransaction = require('../../../models').player_transaction;
const Bidding = require('../../../models').bidding;
const auctionState = require('../state/auctionState');
const SocketUtils = require('../utils/socketUtils');
const CommunicationManager = require('./communicationManager');
const PlayerManager = require('./playerManager');
const BiddingManager = require('./biddingManager');
const UserManager = require('./userManager');
const CONSOLE_LOGGER = require('../../../util/logger');

/**
 * Auction Manager
 * Handles auction lifecycle, timing, and player progression
 */
class AuctionManager {
  /**
   * Start auction with given parameters
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Auction start payload
   */
  static async startAuction(io, socket, payload) {
    console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ called with payload:', JSON.stringify(payload));
    console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ socket.id:', socket.id);

    try {
      // Validate admin user
      const result = await UserManager.validateUser(payload, true);

      // Register user if not already registered
      if (!socket.id) {
        socket.id = result.user.id;
        console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ Setting socket.id to user.id:', socket.id);
      }

      // Check if user is already registered with different socket
      const existingSocketId = auctionState.findUserSocket(result.user.id);
      if (existingSocketId && existingSocketId !== socket.id) {
        console.log(
          '🚀🚀🚀 ~ AuctionManager ~ startAuction ~ User already registered with different socket, removing old socket:',
          existingSocketId
        );
        auctionState.removeRegisteredUser(existingSocketId);
      }

      // Register user with current socket
      if (!auctionState.getUserBySocket(socket.id)) {
        console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ Registering user:', result.user.id, 'with socket:', socket.id);
        auctionState.addRegisteredUser(socket.id, result.user.id);
      }

      // Clear older auction data and reset auction state (including bidding quotas)
      await AuctionManager.clearOlderData();

      // Reset auction state to clear previous bidding quotas
      auctionState.reset();

      // Validate required parameters (both original counts and max counts)
      SocketUtils.validateRequiredParams(payload, [
        'duration', 'averagePlayerCost', 'batter', 'bowler', 'wicketKeeper', 'batterMax', 'bowlerMax', 'wicketKeeperMax'
      ]);

      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ payload keys:', Object.keys(payload));
      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ payload.batter:', payload.batter);
      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ payload.bowler:', payload.bowler);
      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ payload.wicketKeeper:', payload.wicketKeeper);

      // Extract auction parameters
      const {
        duration,
        averagePlayerCost,
        batter,           // Original counts for lineup/player cycle
        bowler,
        wicketKeeper,
        batterMax,        // Max counts for quota validation
        bowlerMax,
        wicketKeeperMax,
        amount = 1000000 // Default amount if not provided
      } = payload;

      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ EXTRACTED VALUES:', {
        'Original counts': { batter, bowler, wicketKeeper },
        'Max counts': { batterMax, bowlerMax, wicketKeeperMax }
      });

      // Set auction configuration
      auctionState.setAuctionTiming(duration, amount);
      auctionState.setAveragePlayerCost(averagePlayerCost);

      // Set original player counts (for lineup/player list)
      auctionState.setOriginalPlayerCounts(batter, bowler, wicketKeeper);

      // Set max bid config using MAX values (for quota validation)
      auctionState.setMaxBidConfig(batterMax, bowlerMax, wicketKeeperMax);

      // Set player types cycle using ORIGINAL counts (for lineup/player list)
      const playerTypesCycle = SocketUtils.setPlayerTypesCycle(batter, bowler, wicketKeeper);
      auctionState.setPlayerTypesCycle(playerTypesCycle);

      // Use MAX values for financial calculations (as this determines user's spending limits)
      const totalPlayersNeeded = batterMax + bowlerMax + wicketKeeperMax;
      const minAvgBalancePerPlayer = payload.averagePlayerCost;
      const minimumSpendAmount = totalPlayersNeeded * minAvgBalancePerPlayer;
      const bonusBalance = amount - minimumSpendAmount;

      // Update user wallet amounts
      await AuctionManager.setUserWalletAmount(amount, bonusBalance);

      // TODO: Save auction configuration to database (temporarily disabled due to model import issue)
      console.log('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ Auction configuration set (database save temporarily disabled)');

      // Start the auction
      auctionState.startAuction();

      // Get first player
      const player = await PlayerManager.getPlayer(io, socket);
      if (player === false) {
        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: false,
            action: payload.action,
            text: 'No players available for auction'
          },
          payload.action
        );
        return;
      }

      CONSOLE_LOGGER.info('startAuction :: Got first player:', { player });

      // Broadcast auction start - use direct io.emit to match original structure
      io.emit(payload.action, {
        player,
        timerDuration: duration,
        amount,
        bonusBalance,
        ...SocketUtils.getPlayersConfigResponse(
          auctionState.getPlayerCycleStatus.playerTypesCycleArr,
          auctionState.getBiddingConfig.maxBidConfig,
          auctionState.getBiddingConfig.averagePlayerCost
        )
      });

      // Handle upcoming players request
      await AuctionManager.handleUpcomingPlayersRequest(io, socket);
      await CommunicationManager.getAndFireLiveUsersEvent(io);
    } catch (error) {
      console.error('🚀🚀🚀 ~ AuctionManager ~ startAuction ~ error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || 'Failed to start auction'
        },
        payload.action
      );
    }
  }

  /**
   * Get another player for auction (admin function)
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Request payload
   */
  static async getAnotherPlayer(io, socket, payload) {
    try {
      await UserManager.validateUser(payload, true);

      if (!auctionState.getUserBySocket(socket.id)) {
        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: false,
            action: payload.action,
            text: 'User not registered'
          },
          payload.action
        );
        return;
      }

      const auctionStatus = auctionState.getAuctionStatus;
      if (auctionStatus.initialDuration === 0) {
        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: 'You need to start auction to get another player'
          },
          payload.action
        );
        return;
      }

      const player = await PlayerManager.getPlayer(io, socket);
      if (player === false) {
        CONSOLE_LOGGER.info('getAnotherPlayer :: No more players exist to be sold.');
        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: 'getNewPlayer',
            totalPlayerLimitReached: true,
            message: 'No more players exist to be sold.'
          },
          'getNewPlayer'
        );
        return;
      }

      // Ensure currentAuctionPlayerId is set
      if (player && player.id) {
        auctionState.setCurrentAuctionPlayer(player.id);
        console.log('🚀🚀🚀 ~ AuctionManager ~ getAnotherPlayer ~ set currentAuctionPlayerId:', player.id);
      }

      CONSOLE_LOGGER.info('Got the Player :: ', { player });
      
      const biddingConfig = auctionState.getBiddingConfig;
      await CommunicationManager.broadcastMessage(io, payload.action, {
        player,
        timerDuration: auctionStatus.initialDuration,
        amount: auctionStatus.initialAmount,
        ...SocketUtils.getPlayersConfigResponse(
          auctionState.getPlayerCycleStatus.playerTypesCycleArr,
          biddingConfig.maxBidConfig,
          biddingConfig.averagePlayerCost
        )
      });
      
      await AuctionManager.handleUpcomingPlayersRequest(io, socket);
    } catch (error) {
      CONSOLE_LOGGER.error('getAnotherPlayer error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || 'Failed to get another player'
        },
        payload.action
      );
    }
  }

  /**
   * Handle last call for current player
   * @param {Object} io - Socket.io instance
   * @param {Object} socket 
   * - Socket instance
   * @param {Object} payload - Last call payload
   */
  static async lastCall(io, socket, payload) {
    console.log('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ called with payload:', payload);
    
    try {
      await UserManager.validateUser(payload, true);

      const auctionStatus = auctionState.getAuctionStatus;
      console.log('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ currentAuctionPlayerId:', auctionStatus.currentAuctionPlayerId);

      if (!auctionStatus.currentAuctionPlayerId) {
        throw new Error('No current auction player found');
      }

      // Activate last call
      auctionState.activateLastCall();

      // Get current player details
      const player = await PlayerManager.getPlayerByPk(auctionStatus.currentAuctionPlayerId);
      if (!player) {
        throw new Error('Current auction player not found in database');
      }

      // Get highest bidder information
      const highestBidInfo = await BiddingManager.getBasePriceAndMaxBid(auctionStatus.currentAuctionPlayerId);

      const lastCallDuration = payload.duration || 30; // Default 30 seconds

      // Set timer for last call
      await AuctionManager.setTimer(
        io,
        socket,
        'lastCall',
        auctionStatus.currentAuctionPlayerId,
        null,
        null,
        lastCallDuration
      );

      // Broadcast last call
      const biddingConfig = auctionState.getBiddingConfig;
      const registeredUsersBefore = auctionState.getRegisteredUsers;
      console.log('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ registeredUsers BEFORE broadcast:', registeredUsersBefore);

      await CommunicationManager.broadcastMessage(io, 'lastCall', {
        player: SocketUtils.modelToPlainObject(player),
        duration: lastCallDuration,
        highestBid: highestBidInfo ? highestBidInfo.maxBid : player.base_price,
        highestBidder: highestBidInfo ? highestBidInfo.highestBidder : null,
        message: `Last call for ${player.name}! ${lastCallDuration} seconds remaining.`,
        ...SocketUtils.getPlayersConfigResponse(
          auctionState.getPlayerCycleStatus.playerTypesCycleArr,
          biddingConfig.maxBidConfig,
          biddingConfig.averagePlayerCost
        )
      });

      const registeredUsersAfter = auctionState.getRegisteredUsers;
      console.log('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ registeredUsers AFTER broadcast:', registeredUsersAfter);
      console.log('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ Last call initiated successfully');
    } catch (error) {
      console.error('🚀🚀🚀 ~ AuctionManager ~ lastCall ~ Error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'lastCall',
          text: error.message || 'Failed to initiate last call'
        },
        'Error'
      );
    }
  }

  /**
   * Set timer for auction events
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {string} action - Timer action
   * @param {number} playerId - Player ID
   * @param {number} boughtPrice - Purchase price (optional)
   * @param {Object} user - User object (optional)
   * @param {number} remainingDuration - Timer duration
   * @param {boolean} pauseActivated - Whether auction is paused
   */
  static async setTimer(io, socket, action, playerId, boughtPrice = null, user = null, remainingDuration, pauseActivated = false) {
    // Only proceed if this is a "Last Call" action or we're already in "Last Call" mode
    const auctionStatus = auctionState.getAuctionStatus;
    if (action !== 'lastCall' && !auctionStatus.isLastCallActive) {
      console.log('🚀🚀🚀 ~ AuctionManager ~ setTimer ~ Not in Last Call mode, timer not started');
      return;
    }

    // Use the current auction player ID if none provided and we're in last call mode
    playerId = playerId || auctionStatus.currentAuctionPlayerId;

    console.log('🚀🚀🚀 ~ AuctionManager ~ setTimer ~ Starting Last Call timer for player:', playerId);
    
    auctionState.activateLastCall();
    
    if (action === 'pauseAuction' && pauseActivated) {
      auctionState.pauseAuction();
      auctionState.deactivateLastCall();
      return;
    }
    
    if (action === 'pauseAuction' && !pauseActivated) {
      const tempObj = auctionState.getTempObj();
      ({ io, socket, action, playerId, boughtPrice, user } = tempObj);
    }
    
    if (action !== 'pauseAuction') {
      auctionState.setTempObj({ io, socket, action, playerId, boughtPrice, user });
    }

    // Use a fixed duration for Last Call (e.g., 30 seconds)
    const lastCallDuration = remainingDuration || 30;

    const timer = setTimeout(async () => {
      if (!auctionState.getAuctionStatus.pauseActivated) {
        auctionState.deactivateLastCall();

        console.log('🚀🚀🚀 ~ AuctionManager ~ setTimer ~ Timer expired for player:', playerId);

        if (boughtPrice === null) {
          // Check if there's a highest bidder for this player
          const highestBidInfo = await BiddingManager.getBasePriceAndMaxBid(playerId);

          if (highestBidInfo && highestBidInfo.highestBidder) {
            // Assign player to highest bidder
            await AuctionManager._handlePlayerSoldToHighestBidder(io, socket, playerId, highestBidInfo);
          } else {
            // Mark as unsold
            await AuctionManager._handlePlayerUnsold(io, playerId);
          }
        } else {
          // Handle direct purchase
          await AuctionManager._handlePlayerSoldDirect(io, socket, playerId, user, boughtPrice);
        }
      }
    }, lastCallDuration * 1000);

    auctionState.setTimer(timer);
  }

  /**
   * Handle player sold to highest bidder
   * @private
   */
  static async _handlePlayerSoldToHighestBidder(io, socket, playerId, highestBidInfo) {
    console.log('🚀🚀🚀 ~ AuctionManager ~ _handlePlayerSoldToHighestBidder ~ Assigning player to highest bidder:', highestBidInfo.highestBidder);

    try {
      // Get the complete user details of the highest bidder
      const highestBidderUser = await User.findByPk(highestBidInfo.highestBidder);

      if (!highestBidderUser) {
        console.error('🚀🚀🚀 ~ AuctionManager ~ _handlePlayerSoldToHighestBidder ~ Could not find highest bidder user with ID:', highestBidInfo.highestBidder);
        throw new Error(`User not found with ID: ${highestBidInfo.highestBidder}`);
      }

      console.log('🚀🚀🚀 ~ AuctionManager ~ _handlePlayerSoldToHighestBidder ~ Highest bidder details:', {
        id: highestBidderUser.id,
        name: highestBidderUser.name,
        remaining_balance: highestBidderUser.remaining_balance,
        bonus_balance: highestBidderUser.bonus_balance
      });

      // Update player as sold to the highest bidder
      const updatedPlayer = await PlayerManager.updatePlayerAsSold(
        playerId,
        highestBidderUser,
        highestBidInfo.maxBid,
        io,
        socket
      );

      // Get the full player details to broadcast
      const playerDetails = await PlayerManager.getPlayerByPk(playerId);
      const plainPlayerDetails = SocketUtils.modelToPlainObject(playerDetails || updatedPlayer[1][0]);

      // Create a success event object for the winning bidder
      const successEvent = {
        user: highestBidderUser,
        player: {
          ...plainPlayerDetails,
          name: plainPlayerDetails.name,
          buy_price: highestBidInfo.maxBid
        }
      };

      const auctionStatus = auctionState.getAuctionStatus;
      const biddingConfig = auctionState.getBiddingConfig;

      // Broadcast the player sold event
      await CommunicationManager.broadcastMessage(
        io,
        'Player sold',
        {
          ...plainPlayerDetails,
          auctionStartedTime: 0,
          remainingTime: 0,
          amount: auctionStatus.initialAmount,
          timerDuration: 0,
          lastCallCompleted: true,
          user: highestBidderUser,
          ...SocketUtils.getPlayersConfigResponse(
            auctionState.getPlayerCycleStatus.playerTypesCycleArr,
            biddingConfig.maxBidConfig,
            biddingConfig.averagePlayerCost
          )
        },
        successEvent
      );
    } catch (error) {
      console.error('🚀🚀🚀 ~ AuctionManager ~ _handlePlayerSoldToHighestBidder ~ Error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'Error',
          text: error.message || 'Failed to update player status'
        },
        'Error'
      );
    }
  }

  /**
   * Handle player marked as unsold
   * @private
   */
  static async _handlePlayerUnsold(io, playerId) {
    console.log('🚀🚀🚀 ~ AuctionManager ~ _handlePlayerUnsold ~ No highest bidder found, marking player as unsold:', playerId);

    const updatedPlayer = await PlayerManager.updatePlayerAsUnsold(playerId);

    // Get the full player details to broadcast
    const playerDetails = await PlayerManager.getPlayerByPk(playerId);

    console.log(
      '🚀🚀🚀 ~ AuctionManager ~ _handlePlayerUnsold ~ Broadcasting player unsold event for:',
      playerDetails ? playerDetails.name : 'Unknown player'
    );

    // Convert to plain object to avoid circular references
    const plainPlayerDetails = SocketUtils.modelToPlainObject(playerDetails || updatedPlayer[1][0]);

    const auctionStatus = auctionState.getAuctionStatus;
    const biddingConfig = auctionState.getBiddingConfig;

    await CommunicationManager.broadcastMessage(io, 'Player unsold', {
      ...plainPlayerDetails,
      auctionStartedTime: 0,
      remainingTime: 0,
      amount: auctionStatus.initialAmount,
      timerDuration: 0,
      lastCallCompleted: true,
      ...SocketUtils.getPlayersConfigResponse(
        auctionState.getPlayerCycleStatus.playerTypesCycleArr,
        biddingConfig.maxBidConfig,
        biddingConfig.averagePlayerCost
      )
    });
  }

  /**
   * Handle direct player purchase
   * @private
   */
  static async _handlePlayerSoldDirect(io, socket, playerId, user, boughtPrice) {
    const updatedPlayer = await PlayerManager.updatePlayerAsSold(playerId, user, boughtPrice, io, socket);

    // Get the full player details to broadcast
    const playerDetails = await PlayerManager.getPlayerByPk(playerId);
    const plainPlayerDetails = SocketUtils.modelToPlainObject(playerDetails || updatedPlayer[1][0]);

    // Create a success event object for the winning bidder
    const successEvent = {
      user: user,
      player: {
        ...plainPlayerDetails,
        name: plainPlayerDetails.name,
        buy_price: boughtPrice
      }
    };

    const auctionStatus = auctionState.getAuctionStatus;
    const biddingConfig = auctionState.getBiddingConfig;

    await CommunicationManager.broadcastMessage(
      io,
      'Player sold',
      {
        ...plainPlayerDetails,
        auctionStartedTime: 0,
        remainingTime: 0,
        amount: auctionStatus.initialAmount,
        timerDuration: 0,
        lastCallCompleted: true,
        user,
        ...SocketUtils.getPlayersConfigResponse(
          auctionState.getPlayerCycleStatus.playerTypesCycleArr,
          biddingConfig.maxBidConfig,
          biddingConfig.averagePlayerCost
        )
      },
      successEvent
    );
  }

  /**
   * Clear older auction data
   */
  static async clearOlderData() {
    try {
      await Bidding.truncate();
      await PlayerTransaction.truncate();
      await Player.update(
        { status: 'not-bought', buy_price: null },
        { where: {} }
      );
      console.log('🚀🚀🚀 ~ AuctionManager ~ clearOlderData ~ Cleared older auction data');
    } catch (error) {
      CONSOLE_LOGGER.error('Error clearing older data:', error);
      throw error;
    }
  }

  /**
   * Set user wallet amounts
   */
  static async setUserWalletAmount(amount, bonusBalance) {
    try {
      const userIds = auctionState.getConnectedUserIds();

      await User.update({ bonus_balance: bonusBalance }, { where: { id: { [Op.in]: userIds } } });

      const usersWithWalletAmount = await User.findAll({
        where: { id: { [Op.in]: userIds } },
        attributes: ['id', 'wallet_amount']
      });

      const userIdsWithBalance = usersWithWalletAmount.filter((user) => user.wallet_amount > 0).map((user) => user.id);

      await User.update(
        { remaining_balance: amount },
        {
          where: {
            id: {
              [Op.and]: [{ [Op.in]: userIds }, { [Op.notIn]: userIdsWithBalance }]
            }
          }
        }
      );
    } catch (error) {
      CONSOLE_LOGGER.error('Error setting user wallet amounts:', error);
      throw error;
    }
  }

  /**
   * Handle upcoming players request
   */
  static async handleUpcomingPlayersRequest(io, socket) {
    console.log("-----------------inside handleUpcomingPlayersRequest------------");
    try {
      const upcomingPlayers = await PlayerManager.getUpcomingPlayers();

      console.log('🚀🚀🚀 ~ AuctionManager ~ handleUpcomingPlayersRequest ~ upcomingPlayers count:', upcomingPlayers.length);

      // Send to all connected clients with proper structure for frontend
      io.emit('getUpcomingPlayers', {
        status: true,
        data: upcomingPlayers,
        action: 'getUpcomingPlayers'
      });
    } catch (error) {
      CONSOLE_LOGGER.error('Error handling upcoming players request:', error);
      io.emit('getUpcomingPlayers', {
        status: false,
        data: [],
        action: 'getUpcomingPlayers',
        error: error.message
      });
    }
  }
}

module.exports = AuctionManager;
