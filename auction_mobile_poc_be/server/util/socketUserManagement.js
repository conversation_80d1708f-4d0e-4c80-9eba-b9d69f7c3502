const jwt = require('jsonwebtoken');
const MOMENT = require('moment');
const _ = require('lodash');
const { User } = require('../models');
const { MESSAGES } = require('./constants');
const CONSOLE_LOGGER = require('./logger');

module.exports = {
  async validateUser(payload, checkAdmin = false) {
    const decodedJwt = jwt.decode(payload.jwt, { complete: true });
    if (!decodedJwt) throw { message: MESSAGES.UNAUTHORIZED, statusCode: 400 };

    const decodedJwtPayload = decodedJwt.payload;
    if (decodedJwtPayload.exp >= MOMENT().utc().unix()) {
      const user = await User.findByPk(decodedJwtPayload.id);
      if (!user) throw { message: MESSAGES.UNAUTHORIZED, statusCode: 400 };

      if (checkAdmin && user.role !== 1) {
        throw { message: MESSAGES.UNAUTHORIZED, statusCode: 400 };
      }
      return { decodedJwtPayload, user };
    }
    throw { message: MESSAGES.REGISTER_FAILED, statusCode: 400 };
  },

  async registerUser(io, socket, payload) {
    try {
      const result = await this.validateUser(payload);
      socket.id = socket.id || result.user.id;

      const existingSocketId = _.findKey(registeredUser, v => v === result.user.id);
      if (existingSocketId) delete registeredUser[existingSocketId];

      if (!_.has(registeredUser, socket.id)) {
        registeredUser[socket.id] = result.user.id;
        // User balance update logic
      }

      // Send response and handle state
    } catch (error) {
      CONSOLE_LOGGER.error('Registration error:', error);
      this.sendResponseToClient(socket, { error: error.message }, 'error');
    }
  },

  async disconnectClient(io, socket) {
    const userId = registeredUser[socket.id];
    if (await this.isUserAdmin(userId)) {
      socket.isDisconnected = true;
    } else {
      delete registeredUser[socket.id];
    }
    // Update live users
  },

  async isUserAdmin(userId) {
    const user = await User.findByPk(userId);
    return user?.role === 1;
  }
};