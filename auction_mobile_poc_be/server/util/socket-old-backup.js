/* eslint-disable max-len */
const jwt = require('jsonwebtoken');
const MOMENT = require('moment');
const _ = require('lodash');
const User = require('../models').user;
const Player = require('../models').player;
const PlayerType = require('../models').player_type;
const Team = require('../models').team;
const Bidding = require('../models').bidding;
const PlayerTransaction = require('../models').player_transaction;
const Transaction = require('../models').player_transaction;
const AuctionDetails = require('../models').auction_details;
const { Sequelize, sequelize } = require('../models/index');
const Constants = require('./constants');
const { Op } = require('sequelize');

const INITIAL_MAX_BID_CONFIG = { BALL: 99, BAT: 99, WK: 99 };

const registeredUser = {};
let countdownTimer;
let duration = 0;
let initialDuration = 0;
let elapsedTime = 0;
let initialAmount = 0;
let auctionStartedTime = 0;
let pauseActivated = false;
let isAuctionStarted = false;
let isLastCallActive = false; // Track if we're in "Last Call" mode
let tempObj = {};
let prevStateObj = [];

/*
This array is being used to store types of players.
When auction starts, filling this array the count of the
specific player passed into [startAuction] event
eg. bowlers:2, batters:1, wickets: 1 -> [BALL, BALL, BAT, WK]
*/
let playerTypesCycleArr = [];
/*
This variable it being used to maintain which player type to find from database
index is getting increment by 1 is successful player retrieve of that type
*/
let playerTypesCycleIndex = 0;
/*
This variable it being used to maintain [playerId]
that which player is already been found while iterating cycle
*/
let playerIdsAlreadyCycled = [];

/*
This variable is being used to store max bidding config for a single user against player type
userId will be key and object with player types with its counts [userId]: { BAT: Number, BALL: Number , WK: Number }
*/
let maxBidConfig = INITIAL_MAX_BID_CONFIG;
let playersBidStatus = {};

/*
This variable is being used to store minimum player requirement to start auction
if any player drops pause the auction
*/
const minimumPlayersRequirementCount = 0;

let averagePlayerCost = 80000;

// Convert Sequelize model to plain object
const modelToPlainObject = (model) => {
  if (!model) return null;

  try {
    // If it's already a plain object, return it
    if (!model.dataValues) return model;

    // Convert to plain object
    const plainObj = model.toJSON ? model.toJSON() : { ...model.dataValues };

    // Handle associations
    Object.keys(plainObj).forEach((key) => {
      if (plainObj[key] && typeof plainObj[key] === 'object') {
        if (Array.isArray(plainObj[key])) {
          plainObj[key] = plainObj[key].map((item) => (item && item.dataValues ? modelToPlainObject(item) : item));
        } else if (plainObj[key].dataValues) {
          plainObj[key] = modelToPlainObject(plainObj[key]);
        }
      }
    });

    return plainObj;
  } catch (error) {
    console.error('Error converting model to plain object:', error);
    return model;
  }
};

// Add this variable to track the current player being auctioned
let currentAuctionPlayerId = null;

class Socket {
  static async fireMinimumPlayerRequirement() {
    const allAdmins = await User.findAll({ where: { role: 1 }, attributes: ['id'] });
    const allAdminIds = allAdmins.map((item) => item.id);

    const registeredUserArray = Object.keys(registeredUser);
    /* removing admin from this array */
    const filteredRegisteredUserArray = registeredUserArray.filter((item) => !allAdminIds.includes(item));

    const payloadToAdmins = {
      isMinimumReached: filteredRegisteredUserArray.length >= minimumPlayersRequirementCount
    };

    this.sendResponseToAdmins('minimumPlayersRequirement', payloadToAdmins, '');
  }

  static async getPlayerByPk(id) {
    try {
      return Player.findByPk(id, {
        include: [
          {
            required: true,
            model: PlayerType
          }
        ]
      });
    } catch (error) {
      return null;
    }
  }

  /**
   *
   * @param {Object} player
   * @param {Object} user
   * @returns {Boolean}
   */
  static isQuotaExhaustedForBidding(player, user) {
    const userMaxBigConfig = this.getPlayerMaxBidConfig(user.id);
    CONSOLE_LOGGER.info('isQuotaExhaustedForBidding ~ userMaxBigConfig:', JSON.stringify(userMaxBigConfig, null, 2));
    if (!userMaxBigConfig) return false;

    return userMaxBigConfig[player.player_type.type].current === userMaxBigConfig[player.player_type.type].max;
  }

  static getPlayersConfigResponse() {
    let batterCount = 0;
    let wicketKeeperCount = 0;
    let bowlerCount = 0;

    playerTypesCycleArr.forEach((item) => {
      if (item === Constants.PLAYER_TYPES.BAT) batterCount += 1;
      else if (item === Constants.PLAYER_TYPES.BALL) bowlerCount += 1;
      else wicketKeeperCount += 1;
    });

    CONSOLE_LOGGER.info('getPlayersConfigResponse ~ playerTypesCycleArr:', playerTypesCycleArr);

    return {
      averagePlayerCost,
      batter: batterCount,
      wicketKeeper: wicketKeeperCount,
      bowler: bowlerCount,
      batterMax: maxBidConfig.BAT,
      wicketKeeperMax: maxBidConfig.WK,
      bowlerMax: maxBidConfig.BALL
    };
  }

  static fireMaxBidEvent(socket, data) {
    Socket.sendResponseToClient(
      socket,
      {
        data,
        socketId: socket.id,
        status: true,
        action: 'maxBidReached',
        text: 'MAX BID REACHED',
        biddable: false
      },
      'maxBidReached'
    );
  }

  static setPlayerTypesCycle(battersCount = 1, bawlersCount = 1, wicketsCount = 1) {
    console.log('🚀🚀🚀 ~ Socket ~ setPlayerTypesCycle ~ called with:', { battersCount, bawlersCount, wicketsCount });

    // Always maintain the order: batsmen first, then bowlers, then wicket keepers
    // Create arrays for each type
    const batsmen = Array(battersCount).fill(Constants.PLAYER_TYPES.BAT);
    const bowlers = Array(bawlersCount).fill(Constants.PLAYER_TYPES.BALL);
    const wicketKeepers = Array(wicketsCount).fill(Constants.PLAYER_TYPES.WK);

    // Combine them in the correct order
    playerTypesCycleArr = [...batsmen, ...bowlers, ...wicketKeepers];

    // Reset the cycle index and already cycled players
    playerTypesCycleIndex = 0;
    playerIdsAlreadyCycled = [];

    console.log('🚀🚀🚀 ~ Socket ~ setPlayerTypesCycle ~ playerTypesCycleArr:', playerTypesCycleArr);
  }

  /**
   *
   * @param {string} userId
   * @returns {{ BAT: { current: number, max: number }, BALL: { current: number, max: number }, WK: { current: number, max: number } }}
   */
  static getPlayerMaxBidConfig(userId) {
    if (!playersBidStatus[userId]) {
      playersBidStatus[userId] = {
        [Constants.PLAYER_TYPES.BALL]: { max: maxBidConfig.BALL, current: 0 },
        [Constants.PLAYER_TYPES.BAT]: { max: maxBidConfig.BAT, current: 0 },
        [Constants.PLAYER_TYPES.WK]: { max: maxBidConfig.WK, current: 0 }
      };
    }

    return playersBidStatus[userId];
  }

  static setMaxBidConfig(batMax, bolMax, wicketMax) {
    maxBidConfig.BAT = batMax;
    maxBidConfig.BALL = bolMax;
    maxBidConfig.WK = wicketMax;
  }

  static async validateUser(payload, checkAdmin = false) {
    console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ called with payload:', JSON.stringify(payload));
    console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ checkAdmin:', checkAdmin);

    const decodedJwt = jwt.decode(payload.jwt, { complete: true });
    if (!decodedJwt) {
      console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ Invalid token format');
      throw {
        message: MESSAGES.UNAUTHORIZED,
        statusCode: 400
      };
    }

    const decodedJwtPayload = decodedJwt.payload;
    console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ decodedJwtPayload:', JSON.stringify(decodedJwtPayload));

    const compareDate = MOMENT().utc().unix();
    if (decodedJwtPayload.exp >= compareDate) {
      const user = await User.findByPk(decodedJwtPayload.id);
      console.log(
        '🚀🚀🚀 ~ Socket ~ validateUser ~ user found:',
        user ? `ID: ${user.id}, Role: ${user.role}` : 'No user found'
      );

      if (user) {
        if (checkAdmin) {
          try {
            const result = { decodedJwtPayload: Socket.checkAdminUser(user, decodedJwtPayload), user };
            console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ Admin check passed');
            return result;
          } catch (error) {
            console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ Admin check failed:', error.message);
            throw error;
          }
        }
        console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ Regular user validation passed');
        return { decodedJwtPayload, user };
      } else {
        console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ User not found in database');
        throw {
          message: MESSAGES.UNAUTHORIZED,
          statusCode: 400
        };
      }
    } else {
      console.log('🚀🚀🚀 ~ Socket ~ validateUser ~ Token expired');
      throw {
        message: MESSAGES.REGISTER_FAILED,
        statusCode: 400
      };
    }
  }

  static checkAdminUser(user, decodedJwtPayload) {
    console.log('🚀🚀🚀 ~ Socket ~ checkAdminUser ~ user role:', user.role);
    if (user.role === 1) {
      console.log('🚀🚀🚀 ~ Socket ~ checkAdminUser ~ Admin role confirmed');
      return decodedJwtPayload;
    } else {
      console.log('🚀🚀🚀 ~ Socket ~ checkAdminUser ~ Not an admin, role:', user.role);
      throw {
        message: MESSAGES.UNAUTHORIZED,
        statusCode: 400
      };
    }
  }

  static async registerUser(io, socket, payload) {
    try {
      const result = await Socket.validateUser(payload);
      if (!socket.id) {
        socket.id = result.user.id;
      }

      const tempStateObj = _.cloneDeep(prevStateObj);
      const searchUser = _.findKey(registeredUser, (value) => value === result.user.id);
      CONSOLE_LOGGER.info('registerUser ~ searchUser:', searchUser);

      if (searchUser) {
        delete registeredUser[searchUser];
      }

      const elapsedTime = Date.now() - auctionStartedTime;
      const remainingTime = Math.round(Math.max(0, duration * 1000 - elapsedTime) / 1000);

      if (!_.has(registeredUser, socket.id)) {
        registeredUser[socket.id] = result.user.id;

        if (isAuctionStarted) {
          try {
            await User.update(
              {
                remaining_balance: initialAmount
              },
              {
                where: {
                  id: result.user.id
                }
              }
            );
          } catch (error) {
            CONSOLE_LOGGER.error('Failed to update user balance:', error);
          }
        } else {
          CONSOLE_LOGGER.info('No ongoing auction, user balance remains the same');
        }

        await this.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: MESSAGES.AUTHORIZED,
            tempStateObj: tempStateObj,
            ongoingAuctionFlag: remainingTime > 0
          },
          payload.action
        );
      } else {
        await this.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: MESSAGES.AUTHORIZED
          },
          payload.action
        );
      }

      if (prevStateObj && prevStateObj.length > 0) {
        let findRemainingTime = remainingTime;
        if (prevStateObj[0] !== 'startAuction') {
          if (prevStateObj && prevStateObj[1] && prevStateObj[1].auctionRemainingTime) {
            findRemainingTime = prevStateObj[1].auctionRemainingTime;
          }
        }
        await this.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: prevStateObj[0],
            remainingTime: findRemainingTime,
            text: {
              ...prevStateObj[1],
              remainingTime: findRemainingTime,
              amount: initialAmount,
              timerDuration: initialDuration
            }
          },
          prevStateObj[0]
        );
      }
      await this.getAndFireLiveUsersEvent(io, socket);
      await this.fireMinimumPlayerRequirement();
      // await this.getAllUserDetails(io, socket, payload);

      CONSOLE_LOGGER.info('registerUser ~ prevStateObj:', prevStateObj);
      CONSOLE_LOGGER.info('registerUser ~ registeredUser:', registeredUser);
    } catch (error) {
      CONSOLE_LOGGER.error('registerUser error:', error);
      await this.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || MESSAGES.UNAUTHORIZED
        },
        'Error'
      );
    }
  }

  static async disconnectClient(io, socket) {
    try {
      // Store the user ID before removing from registeredUser
      const userId = registeredUser[socket.id];
      const isAdmin = userId ? await this.isUserAdmin(userId) : false;
      // If this is an admin and there's an active auction, don't fully clean up
      // Just mark them as disconnected but preserve their state

      if (isAdmin && (isAuctionStarted || pauseActivated)) {
        console.log(`Admin (${userId}) disconnected but auction is active. Preserving state.`);
        // Mark this socket as disconnected but don't remove from registeredUser
        socket.isDisconnected = true;
      } else {
        // For non-admins or when no auction is running, proceed with normal disconnection
        delete registeredUser[socket.id];
      }

      const connectedUserIds = Object.values(registeredUser);

      const records = await User.findAll({
        where: {
          id: { [Op.in]: connectedUserIds },
          role: 2
        },
        attributes: ['id', 'name', 'remaining_balance']
      });

      await this.sendResponseToAdmins('liveUsers', { users: records }, 'Live users fetched');
      await this.fireMinimumPlayerRequirement();

      CONSOLE_LOGGER.info('Client Disconnected > ', socket.id);

      if (typeof socket.close === 'function') {
        socket.close();
      }
    } catch (err) {
      console.error('Error during disconnect:', err);
    }
  }

  /**
   * Helper method to check if a user is an admin
   * @param {*} userId
   * @returns {Promise<boolean>}
   */
  static async isUserAdmin(userId) {
    try {
      const user = await User.findByPk(userId, {
        attributes: ['role']
      });
      return user && user.role === 1; // Assuming role 1 is admin
    } catch (error) {
      console.error('Error checking if user is admin:', error);
      return false;
    }
  }

  static async broadcastMessage(io, action, text, successEvent = null, additionalValues = null) {
    console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ action:', action);
    console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ clients count:', io.sockets.sockets.size);

    prevStateObj = [action, text];

    // Log the message being broadcast for debugging
    if (action === 'Player unsold') {
      console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ Broadcasting Player unsold event');
      if (text && text.dataValues) {
        console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ Player ID:', text.dataValues.id);
        console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ Player Name:', text.dataValues.name);
        console.log('🚀🚀🚀 ~ Socket ~ broadcastMessage ~ Player Status:', text.dataValues.status);
      }
    }

    io.sockets.sockets.forEach(async (socket) => {
      if (successEvent !== null) {
        prevStateObj[0] = 'Bid completed';
        const successUserSocketId = _.findKey(registeredUser, (value) => value === successEvent.user.id);
        if (successUserSocketId === socket.id) {
          const successUserMaxConfig = this.getPlayerMaxBidConfig(registeredUser[socket.id]);
          const playerDetails = await this.getPlayerByPk(successEvent.player.id);

          successUserMaxConfig[playerDetails.player_type.type].current += 1;

          Socket.sendResponseToClient(
            socket,
            {
              socketId: socket.id,
              status: true,
              action: 'Success',
              text: {
                message: `Congratulations you have successfully bought ${successEvent.player.name} for ${successEvent.player.buy_price}!`,
                player: successEvent.player.name,
                user: successEvent.user.name,
                price: successEvent.player.buy_price
              }
            },
            'Success'
          );
          await this.getUserDetails(socket);
        } else {
          Socket.sendResponseToClient(
            socket,
            {
              socketId: socket.id,
              status: true,
              action: 'Sold Player',
              text: {
                message: `${successEvent.user.name} bought ${successEvent.player.name} for ${successEvent.player.buy_price}!`,
                player: successEvent.player.name,
                higghestBidder: successEvent.user.name,
                higghestBid: successEvent.player.buy_price
              }
            },
            'Sold Player'
          );
        }
      } else {
        /*
                ---------- SPECIAL CASE ----------
                From the current logged in user, deciding whether next player is biddable for current user
                */
        const user = { id: registeredUser[socket.id] };
        if (text && text.player && text.player.id) {
          text.text = '';
          const userDetails = await User.findByPk(user.id, { attributes: ['remaining_balance'] });
          if (userDetails) {
            const config = this.getPlayerMaxBidConfig(user.id);
            const remainingValue =
              config.BALL.max -
              config.BALL.current +
              (config.WK.max - config.WK.current) +
              (config.BAT.max - config.BAT.current);
            text.isBelowAverage = userDetails.remaining_balance - remainingValue * averagePlayerCost >= 0;
            CONSOLE_LOGGER.info(
              ' broadcastMessage :: userDetails.remaining_balance : ',
              userDetails.remaining_balance,
              ' remainingValue  : ',
              remainingValue,
              ' averagePlayerCost : ',
              averagePlayerCost
            );
            if (text.isBelowAverage === false) {
              text.text = "Your remaining balance is below the team's average cost.";
            }
          }

          // text.biddable = !this.isQuotaExhaustedForBidding(text.player, user);
          if (text.isBelowAverage) {
            text.biddable = !this.isQuotaExhaustedForBidding(text.player, user);
          }
          CONSOLE_LOGGER.info(' broadcastMessage :: text.biddable : ', text.biddable);
        }

        let responseObj = {
          action,
          text,
          status: true,
          socketId: socket.id,
          ...this.getPlayersConfigResponse()
        };

        if (additionalValues && Object.keys(additionalValues).length) {
          responseObj = { ...responseObj, ...additionalValues };
        }

        Socket.sendResponseToClient(socket, responseObj, action);
      }
      // }
    });
  }

  static async setUserWalletAmount(amount, bonusBalance) {
    const userIds = Object.values(registeredUser);

    await User.update({ bonus_balance: bonusBalance }, { where: { id: { [Op.in]: userIds } } });

    const usersWithWalletAmount = await User.findAll({
      where: {
        id: { [Op.in]: userIds }
      },
      attributes: ['id', 'wallet_amount']
    });

    const userIdsWithBalance = usersWithWalletAmount.filter((user) => user.wallet_amount > 0).map((user) => user.id);

    await User.update(
      { remaining_balance: amount },
      {
        where: {
          id: {
            [Op.and]: [{ [Op.in]: Object.values(registeredUser) }, { [Op.notIn]: userIdsWithBalance }]
          }
        }
      }
    );
  }

  static async updatePlayerAsUnsold(id) {
    console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsUnsold ~ marking player as unsold:', id);
    try {
      const result = await Player.update(
        {
          status: 'unsold'
        },
        {
          where: {
            id
          },
          returning: true
        }
      );

      console.log(
        '🚀🚀🚀 ~ Socket ~ updatePlayerAsUnsold ~ update result:',
        result[0] > 0 ? 'Success' : 'Failed',
        'Rows affected:',
        result[0]
      );

      // Verify the player was actually updated
      const player = await Player.findByPk(id);
      console.log(
        '🚀🚀🚀 ~ Socket ~ updatePlayerAsUnsold ~ player after update:',
        player ? player.status : 'Player not found'
      );

      return result;
    } catch (error) {
      console.error('🚀🚀🚀 ~ Socket ~ updatePlayerAsUnsold ~ error:', error);
      throw error;
    }
  }

  /**
   * Get upcoming players (players available in playerTypesCycleArr)
   * @returns {Array} Array of upcoming players with their details
   */
  static async getUpcomingPlayers() {
    try {
      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ Attempting to fetch upcoming players');
      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ playerTypesCycleArr:', playerTypesCycleArr);
      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ playerTypesCycleIndex:', playerTypesCycleIndex);
      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ playerIdsAlreadyCycled:', playerIdsAlreadyCycled);

      if (!playerTypesCycleArr || playerTypesCycleArr.length === 0) {
        console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ No player types in cycle array');
        return [];
      }

      // Get upcoming player types (excluding the current one)
      let upcomingPlayerTypes = [];

      // Create a copy of the cycle array
      const cycleCopy = [...playerTypesCycleArr];

      // Remove the current player type from the cycle
      cycleCopy.splice(playerTypesCycleIndex, 1);

      // Reorder the array to start from the next index
      if (playerTypesCycleIndex < cycleCopy.length) {
        // If we're not at the end, just take the slice from current index to end, then add the beginning
        upcomingPlayerTypes = [...cycleCopy.slice(playerTypesCycleIndex), ...cycleCopy.slice(0, playerTypesCycleIndex)];
      } else {
        // If we're at the end, just use the array as is (it's already in the right order)
        upcomingPlayerTypes = cycleCopy;
      }

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ upcomingPlayerTypes:', upcomingPlayerTypes);

      if (upcomingPlayerTypes.length === 0) {
        console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ No upcoming player types');
        return [];
      }

      // Get player types from the database
      const playerTypes = await PlayerType.findAll();

      // Create a map of type name to type ID
      const typeToIdMap = {};
      playerTypes.forEach((pt) => {
        typeToIdMap[pt.type] = pt.id;
      });

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ typeToIdMap:', typeToIdMap);

      // Get upcoming player type IDs
      const upcomingTypeIds = upcomingPlayerTypes.map((type) => typeToIdMap[type]).filter((id) => id);

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ upcomingTypeIds:', upcomingTypeIds);

      if (upcomingTypeIds.length === 0) {
        console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ No valid type IDs found');
        return [];
      }

      // Get one player for each upcoming type in the cycle
      const upcomingPlayers = [];
      // Track players we've already added to prevent duplicates
      const addedPlayerIds = new Set();

      // Process each type in order
      for (let i = 0; i < upcomingPlayerTypes.length; i++) {
        const type = upcomingPlayerTypes[i];
        const typeId = typeToIdMap[type];

        if (!typeId) continue;

        // Build the where clause for the query
        const whereClause = {
          status: 'not-bought',
          type_id: typeId
        };

        // Add condition to exclude already cycled players
        if (playerIdsAlreadyCycled.length > 0) {
          whereClause.id = {
            [Op.notIn]: playerIdsAlreadyCycled
          };
        }

        // Add condition to exclude already added players
        if (addedPlayerIds.size > 0) {
          if (whereClause.id) {
            whereClause.id = {
              [Op.and]: [whereClause.id, { [Op.notIn]: Array.from(addedPlayerIds) }]
            };
          } else {
            whereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }
        }

        console.log(
          `🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ whereClause for type ${type}:`,
          JSON.stringify(whereClause)
        );

        // Find one player of this type that hasn't been cycled yet
        let player = await Player.findOne({
          where: whereClause,
          include: [
            {
              model: PlayerType,
              attributes: ['id', 'type'],
              as: 'player_type'
            },
            {
              model: Team,
              attributes: ['id', 'name'],
              as: 'team'
            }
          ],
          raw: false,
          nest: true
        });

        // If no player found with the exclusion filters, try again without the playerIdsAlreadyCycled filter
        if (!player && playerIdsAlreadyCycled.length > 0) {
          console.log(
            `🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ no player found for type ${type} with exclusion filters, trying without playerIdsAlreadyCycled filter`
          );

          // Remove the playerIdsAlreadyCycled filter but keep the addedPlayerIds filter
          const relaxedWhereClause = {
            status: 'not-bought',
            type_id: typeId
          };

          if (addedPlayerIds.size > 0) {
            relaxedWhereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }

          player = await Player.findOne({
            where: relaxedWhereClause,
            include: [
              {
                model: PlayerType,
                attributes: ['id', 'type'],
                as: 'player_type'
              },
              {
                model: Team,
                attributes: ['id', 'name'],
                as: 'team'
              }
            ],
            raw: false,
            nest: true
          });
        }

        if (player) {
          // Add this player ID to our tracking set
          addedPlayerIds.add(player.id);

          // Convert to plain object to avoid circular references
          const plainPlayer = {
            id: player.id,
            name: player.name,
            team_id: player.team_id,
            type_id: player.type_id,
            base_price: player.base_price,
            buy_price: player.buy_price,
            status: player.status,
            player_type: player.player_type
              ? {
                  id: player.player_type.id,
                  type: player.player_type.type
                }
              : null,
            team: player.team
              ? {
                  id: player.team.id,
                  name: player.team.name
                }
              : null
          };

          console.log(`🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ found player for type ${type}:`, plainPlayer.name);
          upcomingPlayers.push(plainPlayer);
        } else {
          console.log(`🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ no player found for type ${type}`);
        }
      }

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayers ~ upcomingPlayers count:', upcomingPlayers.length);

      return upcomingPlayers;
    } catch (error) {
      console.error('Error fetching upcoming players:', error);
      return [];
    }
  }

  static async updatePlayerAsSold(id, user, boughtPrice, io, socket) {
    console.log(
      '🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ called for player:',
      id,
      'user:',
      user.id,
      'price:',
      boughtPrice
    );
    const transaction = await sequelize.transaction();
    let updatedPlayer;
    try {
      // Get the latest auction details to get average_player_cost
      const auctionDetails = await AuctionDetails.findOne({
        order: [['createdAt', 'DESC']]
      });

      const averagePlayerCost = auctionDetails ? auctionDetails.average_player_cost : 0;
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ averagePlayerCost:', averagePlayerCost);

      // Get the count of existing player transactions for this user
      const playerTransactionCount = await PlayerTransaction.count({
        where: { buyer_id: user.id }
      });
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ existing playerTransactionCount:', playerTransactionCount);

      const maxBatsman = auctionDetails.max_batsman;
      const maxBowler = auctionDetails.max_bowler;
      const maxWicketkeeper = auctionDetails.max_wicketkeeper;

      const sum = maxBatsman + maxBowler + maxWicketkeeper;

      // Add 1 to the count to include the current transaction
      const effectiveTransactionCount = playerTransactionCount + 1;
      console.log(
        '🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ effectiveTransactionCount (including current):',
        effectiveTransactionCount
      );

      // Calculate the bonus balance deduction using the effective count
      const bonusDeduction = user.remaining_balance - averagePlayerCost * (sum - effectiveTransactionCount);
      console.log(
        '🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ bonusDeduction with formula: user.remaining_balance - (averagePlayerCost * (sum - effectiveTransactionCount)):',
        bonusDeduction
      );

      // Calculate the final bonus balance by subtracting the bought price
      const finalBonusBalance = bonusDeduction - boughtPrice;
      console.log(
        '🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ finalBonusBalance (bonusDeduction - boughtPrice):',
        finalBonusBalance
      );

      // Verify user has all required fields
      if (!user || !user.id) {
        console.error('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Invalid user object:', user);
        throw new Error('Invalid user object');
      }

      // Get the user's current values before update
      const currentUser = await User.findByPk(user.id);
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Current user values:', {
        id: currentUser.id,
        remaining_balance: currentUser.remaining_balance,
        bonus_balance: currentUser.bonus_balance
      });

      // Calculate new values
      const newRemainingBalance = currentUser.remaining_balance - boughtPrice;

      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Updating user with:', {
        remaining_balance: newRemainingBalance,
        bonus_balance: finalBonusBalance
      });

      // Update user values
      const updateResult = await User.update(
        {
          remaining_balance: newRemainingBalance,
          bonus_balance: finalBonusBalance
        },
        {
          where: { id: user.id },
          transaction,
          returning: true
        }
      );

      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ User update result rows affected:', updateResult[0]);

      // Verify the update
      const updatedUser = await User.findByPk(user.id, { transaction });
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ User after update:', {
        id: updatedUser.id,
        remaining_balance: updatedUser.remaining_balance,
        bonus_balance: updatedUser.bonus_balance
      });

      // Update the player as bought
      updatedPlayer = await Player.update(
        {
          status: 'bought',
          buy_price: boughtPrice
        },
        {
          where: { id },
          returning: true,
          transaction
        }
      );

      // Create a player transaction record
      await PlayerTransaction.create(
        {
          player_id: id,
          buyer_id: user.id
        },
        { transaction }
      );

      await transaction.commit();
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Transaction committed successfully');

      // Double check that the values were updated after transaction commit
      const finalUser = await User.findByPk(user.id);
      console.log('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Final user values after commit:', {
        id: finalUser.id,
        remaining_balance: finalUser.remaining_balance,
        bonus_balance: finalUser.bonus_balance
      });
    } catch (e) {
      console.error('🚀🚀🚀 ~ Socket ~ updatePlayerAsSold ~ Error:', e);
      await transaction.rollback();
      throw e;
    }

    await this.getAndFireLiveUsersEvent(io, socket);
    return updatedPlayer;
  }

  /**
   * Timer function for auction - only active during "Last Call"
   */
  static async setTimer(
    io,
    socket,
    action,
    playerId,
    boughtPrice = null,
    user = null,
    remainingDuration,
    pauseActivated = false
  ) {
    // Only proceed if this is a "Last Call" action or we're already in "Last Call" mode
    if (action !== 'lastCall' && !isLastCallActive) {
      console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ Not in Last Call mode, timer not started');
      return;
    }

    // Use the current auction player ID if none provided and we're in last call mode
    playerId = playerId || currentAuctionPlayerId;

    console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ Starting Last Call timer for player:', playerId);
    isLastCallActive = true;
    auctionStartedTime = Date.now();
    if (countdownTimer) {
      clearTimeout(countdownTimer);
    }
    if (action === 'pauseAuction' && pauseActivated) {
      auctionStartedTime = 0;
      isLastCallActive = false;
    }
    if (action === 'pauseAuction' && !pauseActivated) {
      var {
        // eslint-disable-next-line no-redeclare
        io,
        socket,
        action,
        playerId,
        boughtPrice,
        user
      } = tempObj;
    }
    if (action !== 'pauseAuction') {
      tempObj = {
        io,
        socket,
        action,
        playerId,
        boughtPrice,
        user
      };
    }

    // Use a fixed duration for Last Call (e.g., 30 seconds)
    const lastCallDuration = remainingDuration || 30;

    countdownTimer = setTimeout(async () => {
      if (!pauseActivated) {
        auctionStartedTime = 0;
        isLastCallActive = false;

        // Ensure we're using the correct player ID when the timer expires
        const timerPlayerId = playerId || currentAuctionPlayerId;

        console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ Timer expired for player:', timerPlayerId);

        if (boughtPrice === null) {
          console.log(
            '🚀🚀🚀 ~ Socket ~ setTimer ~ Last Call timer expired, checking for highest bidder:',
            timerPlayerId
          );

          // Check if there's a highest bidder for this player
          const highestBidInfo = await Socket.getBasePriceAndMaxBid(timerPlayerId);

          if (highestBidInfo && highestBidInfo.highestBidder) {
            // If there's a highest bidder, assign the player to them
            console.log(
              '🚀🚀🚀 ~ Socket ~ setTimer ~ Assigning player to highest bidder:',
              highestBidInfo.highestBidder
            );

            try {
              // Get the existing transaction count for the highest bidder
              const existingTransactionCount = await PlayerTransaction.count({
                where: { buyer_id: highestBidInfo.highestBidder }
              });
              console.log(
                '🚀🚀🚀 ~ Socket ~ setTimer ~ Existing transaction count for highest bidder:',
                existingTransactionCount
              );
              console.log(
                '🚀🚀🚀 ~ Socket ~ setTimer ~ Effective transaction count (including current):',
                existingTransactionCount + 1
              );

              // Get the complete user details of the highest bidder with all necessary fields
              const highestBidderUser = await User.findByPk(highestBidInfo.highestBidder);

              if (!highestBidderUser) {
                console.error(
                  '🚀🚀🚀 ~ Socket ~ setTimer ~ Could not find highest bidder user with ID:',
                  highestBidInfo.highestBidder
                );
                throw new Error(`User not found with ID: ${highestBidInfo.highestBidder}`);
              }

              console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ Highest bidder details:', {
                id: highestBidderUser.id,
                name: highestBidderUser.name,
                remaining_balance: highestBidderUser.remaining_balance,
                bonus_balance: highestBidderUser.bonus_balance
              });

              // Log bonus balance calculation for clarity
              const bonusCalcLog = {
                initial_remaining_balance: highestBidderUser.remaining_balance,
                bought_price: highestBidInfo.maxBid,
                expected_bonus_deduction:
                  'remaining_balance - (averagePlayerCost * (sum - effectiveTransactionCount)) - bought_price'
              };
              console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ Bonus balance calculation:', bonusCalcLog);

              // Update player as sold to the highest bidder
              const updatedPlayer = await Socket.updatePlayerAsSold(
                timerPlayerId,
                highestBidderUser,
                highestBidInfo.maxBid,
                io,
                socket
              );
              prevStateObj[0] = 'Bid completed';

              // Get the full player details to broadcast
              const playerDetails = await Player.findByPk(timerPlayerId, {
                include: [{ model: PlayerType }, { model: Team }]
              });

              // Convert to plain object to avoid circular references
              const plainPlayerDetails = modelToPlainObject(playerDetails || updatedPlayer[1][0]);

              // Create a success event object for the winning bidder
              const successEvent = {
                user: highestBidderUser,
                player: {
                  ...plainPlayerDetails,
                  name: plainPlayerDetails.name,
                  buy_price: highestBidInfo.maxBid
                }
              };

              // Broadcast the player sold event with success event for the winning bidder
              Socket.broadcastMessage(
                io,
                'Player sold',
                {
                  ...plainPlayerDetails,
                  auctionStartedTime: 0,
                  remainingTime: 0,
                  amount: initialAmount,
                  timerDuration: 0,
                  lastCallCompleted: true,
                  user: highestBidderUser,
                  ...this.getPlayersConfigResponse()
                },
                successEvent
              );
              return;
            } catch (error) {
              console.error('🚀🚀🚀 ~ Socket ~ setTimer ~ Error:', error);
              Socket.sendResponseToClient(
                socket,
                {
                  socketId: socket.id,
                  status: false,
                  action: 'Error',
                  text: error.message || 'Failed to update player status'
                },
                'Error'
              );
            }
          }

          // If no highest bidder found or user not found, mark as unsold (existing behavior)
          console.log('🚀🚀🚀 ~ Socket ~ setTimer ~ No highest bidder found, marking player as unsold:', timerPlayerId);
          const updatedPlayer = await Socket.updatePlayerAsUnsold(timerPlayerId);
          prevStateObj[0] = 'Player unsold';

          // Get the full player details to broadcast
          const playerDetails = await Player.findByPk(timerPlayerId, {
            include: [{ model: PlayerType }, { model: Team }]
          });

          console.log(
            '🚀🚀🚀 ~ Socket ~ setTimer ~ Broadcasting player unsold event for:',
            playerDetails ? playerDetails.name : 'Unknown player'
          );

          // Convert to plain object to avoid circular references
          const plainPlayerDetails = modelToPlainObject(playerDetails || updatedPlayer[1][0]);

          Socket.broadcastMessage(io, 'Player unsold', {
            ...plainPlayerDetails,
            auctionStartedTime: 0,
            remainingTime: 0,
            amount: initialAmount,
            timerDuration: 0,
            lastCallCompleted: true,
            ...this.getPlayersConfigResponse()
          });
        } else {
          const updatedPlayer = await Socket.updatePlayerAsSold(playerId, user, boughtPrice, io, socket);
          prevStateObj[0] = 'Bid completed';

          // Get the full player details to broadcast
          const playerDetails = await Player.findByPk(playerId, {
            include: [{ model: PlayerType }, { model: Team }]
          });

          // Convert to plain object to avoid circular references
          const plainPlayerDetails = modelToPlainObject(playerDetails || updatedPlayer[1][0]);

          // Create a success event object for the winning bidder
          const successEvent = {
            user: user,
            player: {
              ...plainPlayerDetails,
              name: plainPlayerDetails.name,
              buy_price: boughtPrice
            }
          };

          Socket.broadcastMessage(
            io,
            'Player sold',
            {
              ...plainPlayerDetails,
              auctionStartedTime: 0,
              remainingTime: 0,
              amount: initialAmount,
              timerDuration: 0,
              lastCallCompleted: true,
              user,
              ...this.getPlayersConfigResponse()
            },
            successEvent
          );
        }
      }
    }, lastCallDuration * 1000);
  }

  static async getPlayer(io, socket, isRecursive = false) {
    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ called with isRecursive:', isRecursive);
    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ playerTypesCycleIndex:', playerTypesCycleIndex);
    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ playerTypesCycleArr:', playerTypesCycleArr);

    if (isRecursive) {
      const hasNextRecords = await Player.count({ where: { status: 'not-bought' } });
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ hasNextRecords:', hasNextRecords);
      if (!hasNextRecords) {
        return false;
      }
    }

    if (playerTypesCycleIndex >= playerTypesCycleArr.length) {
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ playerTypesCycleIndex >= playerTypesCycleArr.length, returning false');
      // return false;
      playerTypesCycleIndex = 0;
    }

    const where = { status: 'not-bought' };

    if (playerIdsAlreadyCycled.length) {
      where.id = { [Op.notIn]: playerIdsAlreadyCycled };
    }

    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ searching for player with where:', where);
    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ player type:', playerTypesCycleArr[playerTypesCycleIndex]);

    let player = await Player.findOne({
      // order: Sequelize.literal('random()'),
      where,
      include: [
        {
          required: true,
          model: PlayerType,
          where: { type: playerTypesCycleArr[playerTypesCycleIndex] }
        },
        { model: Team }
      ]
    });

    console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ player found:', player ? player.name : 'No player found');

    if (player && player.dataValues && player.dataValues.id) {
      // Add player to already cycled list
      playerIdsAlreadyCycled.push(player.dataValues.id);
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ added player to playerIdsAlreadyCycled:', player.dataValues.id);

      // Clear any existing bids for this player
      await Bidding.destroy({
        where: { player_id: player.dataValues.id }
      });

      // Set the current auction player ID
      currentAuctionPlayerId = player.dataValues.id;
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ set currentAuctionPlayerId:', currentAuctionPlayerId);

      // Start the timer for this player
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ starting timer for player:', player.name);

      // Convert player to plain object to avoid circular references
      const plainPlayer = modelToPlainObject(player);

      // Comment out timer-related code
      // await Socket.setTimer(io, socket, 'Player unsold', player.id);

      /* reset the index, if it reaches the last index of the cycle array */
      // if (playerIdsAlreadyCycled.length >= playerTypesCycleArr.length) {
      //     CONSOLE_LOGGER.warn('All player types cycled with no valid player found.');
      //     return null;
      // } else {
      //     playerTypesCycleIndex += 1;
      //     if (playerTypesCycleIndex >= playerTypesCycleArr.length) {
      //         playerTypesCycleIndex = 0;
      //     }
      //     console.log("🚀🚀🚀 ~ Socket ~ getPlayer ~ updated playerTypesCycleIndex:", playerTypesCycleIndex);
      // }
      playerTypesCycleIndex += 1;
      return plainPlayer;
    } else {
      console.log(
        '🚀🚀🚀 ~ Socket ~ getPlayer ~ no player found for type:',
        playerTypesCycleArr[playerTypesCycleIndex]
      );
      playerTypesCycleIndex += 1;
      if (playerTypesCycleIndex >= playerTypesCycleArr.length) {
        playerTypesCycleIndex = 0;
      }
      console.log('🚀🚀🚀 ~ Socket ~ getPlayer ~ updated playerTypesCycleIndex:', playerTypesCycleIndex);

      // Try again with the next player type
      if (!isRecursive) {
        return await Socket.getPlayer(io, socket, true);
      } else {
        return null;
      }
    }
  }

  static async getAnotherPlayer(io, socket, payload) {
    await Socket.validateUser(payload, true);

    if (_.has(registeredUser, socket.id)) {
      CONSOLE_LOGGER.info('getAnotherPlayer :: IF ', registeredUser, ' :: ', socket.id);
      if (initialDuration !== 0) {
        duration = initialDuration;
        const player = await Socket.getPlayer(io, socket);
        if (player === false) {
          CONSOLE_LOGGER.info('getAnotherPlayer :: No more players exists to sold.');
          await Socket.sendResponseToClient(
            socket,
            {
              socketId: socket.id,
              status: true,
              action: 'getNewPlayer',
              totalPlayerLimitReached: true,
              message: 'No more players exists to sold.'
            },
            'getNewPlayer'
          );
          return;
        }

        // Ensure currentAuctionPlayerId is set (this should be handled in getPlayer, but let's be safe)
        if (player && player.id) {
          currentAuctionPlayerId = player.id;
          console.log('🚀🚀🚀 ~ Socket ~ getAnotherPlayer ~ set currentAuctionPlayerId:', currentAuctionPlayerId);
        }

        CONSOLE_LOGGER.info('Got the Player :: ', { player });
        await Socket.broadcastMessage(io, payload.action, {
          player,
          timerDuration: initialDuration,
          amount: initialAmount,
          ...this.getPlayersConfigResponse()
        });
        await this.handleUpcomingPlayersRequest(io, socket);
      } else {
        Socket.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: 'You need to start auction to get another player'
          },
          payload.action
        );
      }
    } else {
      CONSOLE_LOGGER.info('getAnotherPlayer :: ELSE ', registeredUser, ' :: ', socket.id);
      Socket.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: payload.action,
          text: MESSAGES.UNAUTHORIZED
        },
        payload.action
      );
    }
  }

  static async startAuction(io, socket, payload) {
    console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ called with payload:', JSON.stringify(payload));
    console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ socket.id:', socket.id);
    console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ registeredUser:', JSON.stringify(registeredUser));

    try {
      // Validate the user and check if they're an admin
      const result = await Socket.validateUser(payload, true);

      // If the user is not registered yet, register them
      if (!socket.id) {
        socket.id = result.user.id;
        console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ Setting socket.id to user.id:', socket.id);
      }

      // Check if this user is already registered with a different socket
      const existingSocketId = _.findKey(registeredUser, (value) => value === result.user.id);
      if (existingSocketId && existingSocketId !== socket.id) {
        console.log(
          '🚀🚀🚀 ~ Socket ~ startAuction ~ User already registered with different socket, removing old socket:',
          existingSocketId
        );
        delete registeredUser[existingSocketId];
      }

      // Register the user with this socket if not already registered
      if (!_.has(registeredUser, socket.id)) {
        console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ Registering user:', result.user.id, 'with socket:', socket.id);
        registeredUser[socket.id] = result.user.id;
      }

      // Now proceed with the auction setup
      await Socket.clearOlderData();
      pauseActivated = false;
      duration = payload.duration;
      initialDuration = payload.duration;
      averagePlayerCost = payload.averagePlayerCost;
      let batterMax = payload.batterMax;
      let bowlerMax = payload.bowlerMax;
      let wicketKeeperMax = payload.wicketKeeperMax;
      const totalPlayersNeeded = batterMax + bowlerMax + wicketKeeperMax;
      const minAvgBalancePerPlayer = averagePlayerCost;
      const minimumSpendAmount = totalPlayersNeeded * minAvgBalancePerPlayer;
      const bonusBalance = payload.amount - minimumSpendAmount;

      const user = await User.findByPk(registeredUser[socket.id], { attributes: ['id', 'wallet_amount'] });

      if (user && user.wallet_amount > 0) {
        initialAmount = user.wallet_amount;
      } else {
        initialAmount = payload.amount;
        await Socket.setUserWalletAmount(initialAmount, bonusBalance);
      }

      averagePlayerCost = payload.averagePlayerCost;
      isAuctionStarted = true;

      this.setMaxBidConfig(payload.batterMax, payload.bowlerMax, payload.wicketKeeperMax);
      try {
        await AuctionDetails.create({
          max_batsman: batterMax,
          max_bowler: bowlerMax,
          max_wicketkeeper: wicketKeeperMax,
          assigned_amount: initialAmount,
          average_player_cost: averagePlayerCost
        });
        console.log('Auction data saved to auction_details table successfully.');
      } catch (error) {
        console.error('Error inserting auction data into auction_details table:', error);
        throw error;
      }
      this.setPlayerTypesCycle(payload.batter, payload.bowler, payload.wicketKeeper);

      const player = await Socket.getPlayer(io, socket);

      // Ensure currentAuctionPlayerId is set (this should be handled in getPlayer, but let's be safe)
      if (player && player.id) {
        currentAuctionPlayerId = player.id;
        console.log('🚀🚀🚀 ~ Socket ~ startAuction ~ set currentAuctionPlayerId:', currentAuctionPlayerId);
      }
      io.emit(payload.action, {
        player,
        timerDuration: payload.duration,
        amount: initialAmount,
        bonusBalance,
        ...this.getPlayersConfigResponse()
      });
      await this.handleUpcomingPlayersRequest(io, socket);
      this.getAndFireLiveUsersEvent(io, socket);
    } catch (error) {
      console.error('🚀🚀🚀 ~ Socket ~ startAuction ~ error:', error);
      await this.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || MESSAGES.UNAUTHORIZED
        },
        payload.action
      );
    }
  }

  /**
   * handling the reconnection
   * @param {*} io
   * @param {*} socket
   * @param {*} payload
   */
  static async reConnect(io, socket, payload) {
    const result = await Socket.validateUser(payload);
    const isAdmin = result.user && result.user.role === 1;

    // Check if this user was previously connected with a different socket ID
    const existingSocketId = _.findKey(registeredUser, (value) => value === result.user.id);
    const wasDisconnected =
      existingSocketId &&
      io.sockets.sockets &&
      Array.from(io.sockets.sockets).some((socket) => socket.id === existingSocketId && socket.isDisconnected);

    if (_.has(registeredUser, socket.id)) {
      Socket.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: payload.action,
          text: MESSAGES.USER_EXIST
        },
        payload.action
      );
    } else {
      // Register the user with the new socket
      registeredUser[socket.id] = result.user.id;

      // If this is an admin reconnecting and there was a previous socket marked as disconnected,
      // clean up the old socket entry
      if (isAdmin && wasDisconnected) {
        console.log(`Admin (${result.user.id}) reconnecting. Cleaning up old socket: ${existingSocketId}`);
        delete registeredUser[existingSocketId];
      }

      let tempStateObj = _.cloneDeep(prevStateObj);
      const elapsedTime = Date.now() - auctionStartedTime;
      const remainingTime = Math.round(Math.max(0, duration * 1000 - elapsedTime) / 1000);

      // For admins, ensure we have the current auction player ID
      if (isAdmin) {
        // Check if we need to update currentAuctionPlayerId from prevStateObj
        if (
          prevStateObj &&
          prevStateObj.length > 0 &&
          prevStateObj[1] &&
          prevStateObj[1].player &&
          prevStateObj[1].player.id
        ) {
          // If there's a player in prevStateObj, update currentAuctionPlayerId
          currentAuctionPlayerId = prevStateObj[1].player.id;
          console.log(
            '🚀🚀🚀 ~ Socket ~ reConnect ~ set currentAuctionPlayerId from prevStateObj:',
            currentAuctionPlayerId
          );
        }

        // If auction is active but paused, ensure we restore the correct state
        if (pauseActivated && prevStateObj && prevStateObj.length > 0) {
          console.log('Admin reconnecting to paused auction. Restoring state.');
        }

        // If auction is active and running, ensure we restore the correct state
        if (isAuctionStarted && !pauseActivated && prevStateObj && prevStateObj.length > 0) {
          console.log('Admin reconnecting to active auction. Restoring state.');
        }
      }

      let reconnectData = {};

      if (prevStateObj[0] === 'pauseAuction') {
        // If auction is paused
        reconnectData = {
          ...tempStateObj[1],
          auctionRemainingTime: tempStateObj[tempStateObj.length - 1].remainingDuration,
          amount: initialAmount,
          ongoingAuction: true,
          pauseActivated: true, // Indicate that auction is paused
          ...this.getPlayersConfigResponse()
        };

        tempStateObj[tempStateObj.length - 1] = {
          ...tempStateObj[tempStateObj.length - 1],
          auctionRemainingTime: tempStateObj[tempStateObj.length - 1].remainingDuration,
          amount: initialAmount,
          ongoingAuction: true
        };

        // Include current player information if available
        if (currentAuctionPlayerId) {
          try {
            const currentPlayer = await Socket.getPlayerByPk(currentAuctionPlayerId);
            if (currentPlayer) {
              reconnectData.player = currentPlayer;
            }
          } catch (error) {
            console.error('Error fetching current auction player:', error);
          }
        }

        // Send the data with pauseAuction action
        await Socket.broadcastMessage(io, prevStateObj[0], reconnectData);
      } else if (prevStateObj[0] === 'startAuction' || isAuctionStarted) {
        // If auction is running

        // Get all the necessary data for a fully functioning auction
        reconnectData = {
          auctionRemainingTime: remainingTime,
          ongoingAuction: !!remainingTime,
          amount: initialAmount,
          timerDuration: remainingTime,
          ...this.getPlayersConfigResponse()
        };

        // Include player information
        if (currentAuctionPlayerId) {
          try {
            const currentPlayer = await Socket.getPlayerByPk(currentAuctionPlayerId);
            if (currentPlayer) {
              reconnectData.player = currentPlayer;

              // For admins, also include current bidding information
              if (isAdmin) {
                try {
                  const currentBids = await Bidding.findAll({
                    where: { player_id: currentAuctionPlayerId },
                    order: [['createdAt', 'DESC']],
                    limit: 5,
                    include: [{ model: User, attributes: ['id', 'name'] }]
                  });

                  if (currentBids && currentBids.length > 0) {
                    reconnectData.currentBids = currentBids.map((bid) => ({
                      userId: bid.user_id,
                      userName: bid.user ? bid.user.name : 'Unknown',
                      amount: bid.bid_amount,
                      timestamp: bid.createdAt
                    }));
                  }
                } catch (error) {
                  console.error('Error fetching current bids:', error);
                }
              }
            }
          } catch (error) {
            console.error('Error fetching current auction player:', error);
          }
        }

        const tempArray = [null, reconnectData];
        tempStateObj = [...tempStateObj, ...tempArray];

        // Send the data with startAuction action to properly initialize the client
        await Socket.broadcastMessage(io, 'startAuction', reconnectData);

        // Update upcoming players
        await this.handleUpcomingPlayersRequest(io, socket);
      } else {
        // No auction in progress
        const tempArray = [
          null,
          { auctionRemainingTime: remainingTime, ongoingAuction: !!remainingTime, amount: initialAmount }
        ];
        tempStateObj = [...tempStateObj, ...tempArray];
        await Socket.broadcastMessage(io, prevStateObj[0], tempStateObj[1]);
      }
    }
  }
  /**
   * handling the auctionStatus
   * @param {*} io
   * @param {*} socket
   * @param {*} payload
   */
  static async auctionStatus(io, socket, payload) {
    const result = await Socket.validateUser(payload);
    const isAdmin = result.user && result.user.role === 1;

    // Prepare response data
    let responseData = {
      socketId: socket.id,
      status: false,
      action: payload.action,
      text: MESSAGES.UNAUTHORIZED
    };

    if (prevStateObj && prevStateObj.length > 0) {
      CONSOLE_LOGGER.info('auctionStatus :: IF prevState lenght > 0 ', prevStateObj.length);

      // Check if we need to update currentAuctionPlayerId from prevStateObj
      if (prevStateObj[1] && prevStateObj[1].player && prevStateObj[1].player.id) {
        // If there's a player in prevStateObj, update currentAuctionPlayerId
        currentAuctionPlayerId = prevStateObj[1].player.id;
        console.log(
          '🚀🚀🚀 ~ Socket ~ auctionStatus ~ set currentAuctionPlayerId from prevStateObj:',
          currentAuctionPlayerId
        );
      }

      // For admins, provide more detailed auction status
      if (isAdmin) {
        responseData = {
          socketId: socket.id,
          status: true,
          action: payload.action,
          text: MESSAGES.AUTHORIZED,
          auctionActive: isAuctionStarted,
          auctionPaused: pauseActivated,
          currentPlayerId: currentAuctionPlayerId,
          remainingTime: duration,
          initialAmount: initialAmount,
          playerTypesCycleIndex: playerTypesCycleIndex,
          playerTypesCycleArr: playerTypesCycleArr,
          maxBidConfig: maxBidConfig
        };

        // Include current player information if available
        if (currentAuctionPlayerId) {
          try {
            const currentPlayer = await Socket.getPlayerByPk(currentAuctionPlayerId);
            if (currentPlayer) {
              responseData.currentPlayer = currentPlayer;

              // Also include current bidding information
              try {
                const currentBids = await Bidding.findAll({
                  where: { player_id: currentAuctionPlayerId },
                  order: [['createdAt', 'DESC']],
                  limit: 5,
                  include: [{ model: User, attributes: ['id', 'name'] }]
                });

                if (currentBids && currentBids.length > 0) {
                  responseData.currentBids = currentBids.map((bid) => ({
                    userId: bid.user_id,
                    userName: bid.user ? bid.user.name : 'Unknown',
                    amount: bid.bid_amount,
                    timestamp: bid.createdAt
                  }));
                }
              } catch (error) {
                console.error('Error fetching current bids:', error);
              }
            }
          } catch (error) {
            console.error('Error fetching current auction player:', error);
          }
        }
      } else {
        // For regular users, provide basic auction status
        responseData = {
          socketId: socket.id,
          status: true,
          action: payload.action,
          text: MESSAGES.AUTHORIZED,
          auctionActive: isAuctionStarted,
          auctionPaused: pauseActivated
        };
      }
    }

    Socket.sendResponseToClient(socket, responseData, payload.action);
  }
  /**
   * handling the auctionCompleted
   * @param {*} io
   * @param {*} socket
   * @param {*} payload
   */
  static async auctionCompleted(io, socket, payload) {
    await Socket.validateUser(payload);

    Socket.broadcastMessage(io, payload.action, 'Auction completed', null, null);

    const userIds = Object.values(registeredUser);
    console.log('🚀 ~ file: socket.js:886 ~ Socket ~ auctionCompleted ~ userIds:', userIds);

    if (userIds.length > 0) {
      await User.update(
        {
          remaining_balance: 0,
          wallet_amount: 0,
          bonus_balance: 0
        },
        {
          where: {
            id: userIds
          }
        }
      );

      // Get all socket IDs for the users
      const socketIds = Object.keys(registeredUser);
      for (const socketId of socketIds) {
        const targetSocket = Array.from(io.sockets.sockets.values()).find((socket) => socket.id === socketId);

        if (targetSocket) {
          try {
            await Socket.getUserDetails(targetSocket);
          } catch (error) {
            console.error(`Error getting user details for socket ${socketId}:`, error);
          }
        }
      }
    }

    // Reset all auction-related variables
    countdownTimer = null;
    duration = 0;
    initialDuration = 0;
    elapsedTime = 0;
    initialAmount = 0;
    auctionStartedTime = 0;
    pauseActivated = false;
    tempObj = {};
    prevStateObj = [];
    playerTypesCycleArr = [];
    playerTypesCycleIndex = 0;
    maxBidConfig = INITIAL_MAX_BID_CONFIG;
    playersBidStatus = {};
    playerIdsAlreadyCycled = [];
    averagePlayerCost = 0;
    isAuctionStarted = false;
    isLastCallActive = false;

    // Reset the current auction player ID
    currentAuctionPlayerId = null;
    console.log('🚀🚀🚀 ~ Socket ~ auctionCompleted ~ reset currentAuctionPlayerId to null');
  }

  static async getBasePriceAndMaxBid(playerId) {
    const res = await Bidding.findOne({
      where: {
        player_id: playerId,
        highest_bid: {
          [Sequelize.Op.eq]: Sequelize.literal(
            `(SELECT MAX("highest_bid") FROM "biddings" WHERE "player_id" = '${playerId}')`
          )
        }
      },
      include: [Player]
    });
    if (res !== null) {
      // Only check player status if we're not in last call timer expiration context
      if (res.dataValues.player.dataValues.status !== 'not-bought' && !isLastCallActive) {
        throw {
          message: MESSAGES.INVALID_REQUEST,
          statusCode: 400
        };
      }
    }
    return res !== null
      ? {
          maxBid: res.dataValues.highest_bid,
          highestBidder: res.dataValues.highest_bidder
        }
      : null;
  }

  static async addBiddingRecord(user, bid, playerId) {
    return await Bidding.create({
      player_id: playerId,
      highest_bidder: user.id,
      highest_bid: bid
    });
  }

  static async checkCanUserBid(_, socket, priceObj, bid, player, user) {
    const maxBidConfig = this.getPlayerMaxBidConfig(user.id);
    CONSOLE_LOGGER.info('checkCanUserBid :: maxBidConfig : ', { maxBidConfig });
    const remainingValue =
      maxBidConfig.BALL.max -
      maxBidConfig.BALL.current +
      (maxBidConfig.WK.max - maxBidConfig.WK.current) +
      (maxBidConfig.BAT.max - maxBidConfig.BAT.current);
    CONSOLE_LOGGER.info(
      'checkCanUserBid :: remainingValue : ',
      remainingValue,
      'before :: user remaining balance ',
      user.remaining_balance,
      ' bid : ',
      bid
    );
    let userRemaingValue = user.remaining_balance;
    userRemaingValue -= bid;
    CONSOLE_LOGGER.info(
      'checkCanUserBid :: user.remaining_balance:  ',
      user.remaining_balance,
      ' userRemaingValue:  ',
      userRemaingValue,
      ' priceObj : ',
      { priceObj },
      ' remainingValue : ',
      remainingValue,
      ' averagePlayerCost : ',
      averagePlayerCost
    );
    if (userRemaingValue < (remainingValue - 1) * averagePlayerCost) {
      throw {
        actionType: 'balanceBelowAverage',
        message: "Your remaining balance is below the team's average cost.",
        statusCode: 400,
        biddable: true,
        isBelowAverage: false,
        text: "Your remaining balance is below the team's average cost.",
        timerDuration: initialDuration,
        remainingTime: Math.round(Math.max(0, duration * 1000 - elapsedTime) / 1000),
        ...this.getPlayersConfigResponse()
      };
    }
    CONSOLE_LOGGER.info(
      'checkCanUserBid :: maxBidConfig[player.player_type.type].current : ',
      maxBidConfig[player.player_type.type].current,
      ' maxBidConfig[player.player_type.type].max : ',
      maxBidConfig[player.player_type.type].max
    );

    if (maxBidConfig[player.player_type.type].current === maxBidConfig[player.player_type.type].max) {
      throw {
        actionType: 'maxBidReached',
        message: 'MAX BID REACHED',
        biddable: true,
        timerDuration: duration,
        remainingTime: Math.round(Math.max(0, duration * 1000 - elapsedTime) / 1000),
        statusCode: 400
      };
    }
    CONSOLE_LOGGER.info('Max BID REACH', bid);
    console.log('🚀 ~ Socket ~ checkCanUserBid ~ player.dataValues.base_price:', player.dataValues.base_price);
    console.log('🚀 ~ Socket ~ checkCanUserBid ~ user.id:', user.id);
    console.log(
      '🚀 ~ Socket ~ checkCanUserBid ~ priceObj.highestBidder:',
      priceObj?.highestBidder != null ? priceObj.highestBidder : 'not-bought'
    );
    console.log('🚀 ~ Socket ~ checkCanUserBid ~  user.remaining_balance:', user.remaining_balance);
    if (priceObj === null && user.remaining_balance >= bid && bid >= player.dataValues.base_price) {
      CONSOLE_LOGGER.info(
        'checkCanUserBid :: priceObj === null && userRemaingValue >= bid && bid >= player.dataValues.base_price'
      );

      if (maxBidConfig[player.player_type.type].current === maxBidConfig[player.player_type.type].max) {
        CONSOLE_LOGGER.info(
          'checkCanUserBid :: maxBidConfig[player.player_type.type].current === maxBidConfig[player.player_type.type].max'
        );
        this.fireMaxBidEvent(socket, player);
      }

      return await Socket.addBiddingRecord(user, bid, player.dataValues.id);
    } else if (
      bid > priceObj.maxBid &&
      bid >= player.dataValues.base_price &&
      user.remaining_balance >= bid &&
      priceObj.highestBidder !== user.id
    ) {
      CONSOLE_LOGGER.info(
        'checkCanUserBid :: bid > priceObj.maxBid && bid >= player.dataValues.base_price && userRemaingValue >= bid && priceObj.highestBidder !== user.id'
      );
      if (maxBidConfig[player.player_type.type].current === maxBidConfig[player.player_type.type].max) {
        CONSOLE_LOGGER.info(
          'checkCanUserBid :: maxBidConfig[player.player_type.type].current === maxBidConfig[player.player_type.type].max'
        );
        this.fireMaxBidEvent(socket, player);
      }

      return await Socket.addBiddingRecord(user, bid, player.dataValues.id);
    } else {
      CONSOLE_LOGGER.info('checkCanUserBid :: else');
      throw {
        message: MESSAGES.INVALID_REQUEST,
        statusCode: 400
      };
    }
  }

  static async addBid(io, socket, payload) {
    console.log('🚀🚀🚀 ~ Socket ~ addBid ~ called with payload:', payload);

    try {
      const result = await Socket.validateUser(payload);

      if (_.has(registeredUser, socket.id)) {
        // If we're in "Last Call" mode, cancel the timer
        const player = await Player.findByPk(payload.playerId, { include: [PlayerType] });

        // Update currentAuctionPlayerId with this player's ID
        if (player && player.dataValues && player.dataValues.id) {
          currentAuctionPlayerId = player.dataValues.id;
          console.log('🚀🚀🚀 ~ Socket ~ addBid ~ set currentAuctionPlayerId:', currentAuctionPlayerId);
        }

        const priceObj = await Socket.getBasePriceAndMaxBid(payload.playerId);
        CONSOLE_LOGGER.info('addBid :: priceObj : ', priceObj);
        const bid = await Socket.checkCanUserBid(io, socket, priceObj, payload.bid, player, result.user);

        if (isLastCallActive && countdownTimer) {
          console.log('🚀🚀🚀 ~ Socket ~ addBid ~ Cancelling Last Call timer due to new bid');
          clearTimeout(countdownTimer);
          isLastCallActive = false;
        }

        duration = initialDuration;

        console.log('🚀 ~ Socket ~ addBid ~ bid:', bid);

        // Comment out timer-related code
        // await Socket.setTimer(io, socket, null, payload.playerId, payload.bid, result.user);
        // prevStateObj = ['resetLastCallTimer', {
        //     higghestBid: payload.bid,
        // }];
        await Socket.broadcastMessage(io, 'resetLastCallTimer', {
          higghestBid: payload.bid,
          higghestBidder: result.user.name,
          higghestBidderId: result.user.id,
          player: player,
          bidId: bid.id,
          timerDuration: initialDuration,
          lastCallActive: false, // Indicate that Last Call is no longer active
          ...this.getPlayersConfigResponse()
        });
      }
    } catch (error) {
      CONSOLE_LOGGER.error('addBid :: ERROR ', error);
      Socket.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'Error',
          text: error.message
        },
        'Error'
      );
    }
  }

  static async getAndFireLiveUsersEvent(io) {
    try {
      const connectedUserIds = [];
      io.sockets.sockets.forEach((socket) => {
        const userId = registeredUser[socket.id];
        if (userId) {
          connectedUserIds.push(userId);
        }
      });

      const playerTypeCounts = await Player.findAll({
        attributes: ['type_id', [sequelize.fn('COUNT', sequelize.col('player.id')), 'count']],
        include: [{ model: PlayerType, attributes: ['type'] }],
        group: ['type_id', 'player_type.id', 'player_type.type'],
        raw: true
      });

      const connectedUserDetails = await User.findAll({
        where: { id: { [Op.in]: connectedUserIds }, role: 2 },
        include: [
          {
            model: Transaction,
            include: [{ model: Player, include: [PlayerType, Team] }]
          }
        ]
      });

      const typeCountsObject = playerTypeCounts.reduce((acc, curr) => {
        const typeMapping = {
          BALL: 'totalBowler',
          WK: 'totalWK',
          BAT: 'totalBatsman'
        };

        const key = typeMapping[curr['player_type.type']] || curr['player_type.type'];
        acc[key] = parseInt(curr.count);
        return acc;
      }, {});

      const auctionData = await AuctionDetails.findOne({
        attributes: ['assigned_amount', 'max_batsman', 'max_bowler', 'max_wicketkeeper', 'assigned_amount'],
        order: [['createdAt', 'DESC']]
      });

      const auctionSummary = auctionData ? auctionData.dataValues : {};
      const auctionInfo = {
        assigned_amount: auctionSummary.assigned_amount || 0,
        max_batsman: auctionSummary.max_batsman || 0,
        max_bowler: auctionSummary.max_bowler || 0,
        max_wicketkeeper: auctionSummary.max_wicketkeeper || 0,
        wallet_amount: auctionSummary.wallet_amount || 0
      };

      const sanitizedUsers = await Promise.all(
        connectedUserDetails.map(async (user) => {
          const userObj = user.dataValues;
          delete userObj.password;

          const totalPurchased = await Transaction.count({ where: { buyer_id: user.id } });

          const bowlers = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [{ model: PlayerType, required: true, where: { type: 'BALL' } }]
              }
            ],
            where: { buyer_id: user.id }
          });

          const batsmen = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [{ model: PlayerType, required: true, where: { type: 'BAT' } }]
              }
            ],
            where: { buyer_id: user.id }
          });

          const wicketKeepers = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [{ model: PlayerType, required: true, where: { type: 'WK' } }]
              }
            ],
            where: { buyer_id: user.id }
          });

          userObj.purchaseSummary = { totalPurchased, bowlers, batsmen, wicketKeepers };
          return userObj;
        })
      );

      const response = {
        users: sanitizedUsers,
        playerTypeCounts: typeCountsObject,
        auctionInfo: auctionInfo
      };

      const records = await User.findAll({
        where: { id: { [Op.in]: connectedUserIds }, role: 2 },
        attributes: ['id', 'name', 'remaining_balance']
      });

      await this.sendResponseToAdmins('liveUsers', { users: records }, 'Live users fetched');
      io.emit('liveBidders', response);
    } catch (error) {
      CONSOLE_LOGGER.error('getAndFireLiveUsersEvent error:', error);
      io.emit('error', {
        status: false,
        action: 'getAndFireLiveUsersEvent',
        text: error.message || 'Failed to get live users'
      });
    }
  }

  static async sendResponseToAdmins(action, data, text) {
    try {
      const allAdmins = await User.findAll({ where: { role: 1 }, attributes: ['id'] });
      const allAdminIds = allAdmins.map((item) => item.id);

      const socketIds = _.map(allAdminIds, (userId) => _.findKey(registeredUser, (value) => value === userId)).filter(
        Boolean
      );
      const io = global.IO;

      socketIds.forEach((socketId) => {
        const targetSocket = Array.from(io.sockets.sockets.values()).find((socket) => socket.id === socketId);
        if (targetSocket) {
          targetSocket.emit(action, {
            action,
            data,
            text,
            socketId,
            status: true
          });
        }
      });
    } catch (error) {
      CONSOLE_LOGGER.error('sendResponseToAdmins error:', error);

      const io = global.IO;
      io.emit('error', {
        status: false,
        action: 'sendResponseToAdmins',
        text: error.message || 'Failed to send response to admins'
      });
    }
  }

  static sendResponseToClient(socket, responseObj, eventName) {
    CONSOLE_LOGGER.info('Socket ~ sendResponseToClient ~ socket.id:', socket.id);

    if (!socket) {
      console.error('Socket ~ sendResponseToClient ~ Invalid Socket');
      return;
    }

    try {
      // Send the response to the specific client
      socket.emit(eventName, responseObj);
    } catch (error) {
      console.error('Error sending response to client:', error);
    }
  }

  static async clearOlderData() {
    await Bidding.truncate();
    await PlayerTransaction.truncate();
    return await Player.update(
      {
        status: 'not-bought'
      },
      { where: {} }
    );
  }

  /**
   * Handle request for upcoming players (players available in playerTypesCycleArr)
   * @param {*} io WebSocket server
   * @param {*} socket WebSocket client
   */
  static async handleUpcomingPlayersRequest(io, socket) {
    console.log("-----------------inside handleUpcomingPlayersRequest------------");
    try {
      // Debug: Check if playerIdsAlreadyCycled contains too many IDs
      const totalNotBoughtPlayers = await Player.count({ where: { status: 'not-bought' } });
      console.log('🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ totalNotBoughtPlayers:', totalNotBoughtPlayers);
      console.log('🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ playerIdsAlreadyCycled:', playerIdsAlreadyCycled);
      console.log(
        '🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ playerIdsAlreadyCycled.length:',
        playerIdsAlreadyCycled.length
      );

      // If more than half of not-bought players are in the already cycled list, reset it
      if (playerIdsAlreadyCycled.length > totalNotBoughtPlayers / 2) {
        console.log(
          '🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ Resetting playerIdsAlreadyCycled because it contains too many IDs'
        );
        playerIdsAlreadyCycled = [];
      }

      // Get player types in the cycle
      const playerTypesInCycle = playerTypesCycleArr || [];

      // Get the current player type
      const currentPlayerType =
        playerTypesCycleIndex < playerTypesInCycle.length ? playerTypesInCycle[playerTypesCycleIndex] : null;

      // Get upcoming player types (excluding current) - ONLY FROM THE CURRENT CYCLE
      let upcomingPlayerTypes = [];
      if (playerTypesInCycle.length > 1) {
        console.log('🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ playerTypesInCycle:', playerTypesInCycle);
        // Only include player types from the current index to the end of the array
        // This ensures we don't wrap around to the next cycle
        upcomingPlayerTypes = playerTypesInCycle.slice(playerTypesCycleIndex);
      }

      console.log('🚀🚀🚀 ~ Socket ~ handleUpcomingPlayersRequest ~ upcomingPlayerTypes:', upcomingPlayerTypes);

      // Get upcoming players (players in the cycle array that haven't been auctioned yet)
      // We'll use the updated upcomingPlayerTypes array
      const upcomingPlayers = await Socket.getUpcomingPlayersForTypes(upcomingPlayerTypes);

      // Get a count of players by status for debugging
      const playerCounts = {};
      const statuses = await Player.findAll({
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['status'],
        raw: true
      });

      statuses.forEach((status) => {
        playerCounts[status.status] = parseInt(status.count);
      });

      // Get counts of players by type
      const playerCountsByType = {};
      const playerTypesCounts = await Player.findAll({
        attributes: [
          [sequelize.col('player_type.type'), 'type'],
          [sequelize.fn('COUNT', sequelize.col('player.id')), 'count']
        ],
        include: [
          {
            model: PlayerType,
            attributes: []
          }
        ],
        where: {
          status: 'not-bought'
        },
        group: [sequelize.col('player_type.type')],
        raw: true
      });

      playerTypesCounts.forEach((item) => {
        playerCountsByType[item.type] = parseInt(item.count);
      });

      // Create a safe response object without circular references
      const responseData = {
        upcomingPlayers,
        cycle: {
          playerTypesInCycle,
          currentPlayerType,
          upcomingPlayerTypes,
          currentCycleIndex: playerTypesCycleIndex,
          totalTypesInCycle: playerTypesInCycle.length,
          playerIdsAlreadyCycled: playerIdsAlreadyCycled.slice() // Create a copy of the array
        },
        debug: {
          playerCounts,
          playerCountsByType,
          totalPlayers: statuses.reduce((sum, status) => sum + parseInt(status.count), 0)
        }
      };

      io.emit('getUpcomingPlayers', { data: upcomingPlayers });
    } catch (error) {
      console.error('Error handling upcoming players request:', error);
      await this.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'Error',
          text: error.message || 'Failed to retrieve upcoming players',
          error: error.toString()
        },
        'Error'
      );
    }
  }

  /**
   * Handle Last Call action - starts a timer for the current player
   * @param {*} io WebSocket server
   * @param {*} socket WebSocket client
   * @param {*} payload Request payload
   */
  static async lastCall(io, socket, payload) {
    console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ called with payload:', payload);
    console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ prevStateObj:', JSON.stringify(prevStateObj));
    console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ currentAuctionPlayerId:', currentAuctionPlayerId);

    try {
      await Socket.validateUser(payload, true);

      if (_.has(registeredUser, socket.id)) {
        // Try to get the player ID from different sources
        let playerId = payload.playerId;

        // If no player ID in payload, try to get from prevStateObj
        if (!playerId && prevStateObj && prevStateObj.length > 0 && prevStateObj[1] && prevStateObj[1].player) {
          playerId = prevStateObj[1].player.id;
          console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ Using player ID from prevStateObj:', playerId);
        }

        // If still no player ID, use the current auction player ID
        if (!playerId) {
          playerId = currentAuctionPlayerId;
          console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ Using currentAuctionPlayerId:', playerId);
        }

        // If still no player ID, throw an error
        if (!playerId) {
          console.log(
            '🚀🚀🚀 ~ Socket ~ lastCall ~ No player ID found in payload, prevStateObj, or currentAuctionPlayerId'
          );
          throw {
            message: 'No player currently being auctioned. Please start an auction or bring up a player first.',
            statusCode: 400
          };
        }

        // Get the player details
        const player = await Player.findByPk(playerId, {
          include: [{ model: PlayerType }, { model: Team }]
        });

        if (!player) {
          console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ Player not found with ID:', playerId);
          throw new Error(`Player not found with ID: ${playerId}`);
        }

        console.log('🚀🚀🚀 ~ Socket ~ lastCall ~ Starting Last Call for player:', player.name);

        // Convert to plain object to avoid circular references
        const plainPlayer = modelToPlainObject(player);

        // If there's an existing last call timer, clear it
        if (countdownTimer) {
          clearTimeout(countdownTimer);
        }

        // Set the last call active flag
        isLastCallActive = true;

        // Start the timer for Last Call (default 30 seconds)
        const lastCallDuration = payload.duration || 30;
        await Socket.setTimer(io, socket, 'lastCall', playerId, null, null, lastCallDuration);

        // Broadcast the Last Call event
        await Socket.broadcastMessage(io, 'lastCall', {
          player: plainPlayer,
          lastCallActive: true,
          timerDuration: lastCallDuration,
          remainingTime: lastCallDuration,
          amount: initialAmount,
          ...this.getPlayersConfigResponse()
        });
      } else {
        CONSOLE_LOGGER.info('lastCall :: ELSE ', registeredUser, ' :: ', socket.id);
        Socket.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: false,
            action: payload.action,
            text: MESSAGES.UNAUTHORIZED
          },
          payload.action
        );
      }
    } catch (error) {
      console.error('🚀🚀🚀 ~ Socket ~ lastCall ~ error:', error);
      Socket.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || MESSAGES.UNAUTHORIZED
        },
        'Error'
      );
    }
  }

  /**
   * Get upcoming players for specific player types
   * @param {Array<string>} playerTypes Array of player types (e.g. ["BAT", "BALL", "WK"])
   * @returns {Promise<Array>} Array of player objects
   */
  static async getUpcomingPlayersForTypes(playerTypes) {
    try {
      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ playerTypes:', playerTypes);

      if (!playerTypes || playerTypes.length === 0) {
        console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ No player types provided');
        return [];
      }

      // Get player types from the database
      const dbPlayerTypes = await PlayerType.findAll();

      // Create a map of type name to type ID
      const typeToIdMap = {};
      dbPlayerTypes.forEach((pt) => {
        typeToIdMap[pt.type] = pt.id;
      });

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ typeToIdMap:', typeToIdMap);

      // Get player type IDs
      const typeIds = playerTypes.map((type) => typeToIdMap[type]).filter((id) => id);

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ typeIds:', typeIds);

      if (typeIds.length === 0) {
        console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ No valid type IDs found');
        return [];
      }

      // Get one player for each type in the provided array
      const upcomingPlayers = [];
      // Track players we've already added to prevent duplicates
      const addedPlayerIds = new Set();

      // Process each type in order
      for (let i = 0; i < playerTypes.length; i++) {
        const type = playerTypes[i];
        const typeId = typeToIdMap[type];

        if (!typeId) continue;

        // Build the where clause for the query
        const whereClause = {
          status: 'not-bought',
          type_id: typeId
        };

        // Add condition to exclude already cycled players
        if (playerIdsAlreadyCycled.length > 0) {
          whereClause.id = {
            [Op.notIn]: playerIdsAlreadyCycled
          };
        }

        // Add condition to exclude already added players
        if (addedPlayerIds.size > 0) {
          if (whereClause.id) {
            whereClause.id = {
              [Op.and]: [whereClause.id, { [Op.notIn]: Array.from(addedPlayerIds) }]
            };
          } else {
            whereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }
        }

        console.log(
          `🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ whereClause for type ${type}:`,
          JSON.stringify(whereClause)
        );

        // Find one player of this type that hasn't been cycled yet
        let player = await Player.findOne({
          where: whereClause,
          include: [
            {
              model: PlayerType,
              attributes: ['id', 'type'],
              as: 'player_type'
            },
            {
              model: Team,
              attributes: ['id', 'name'],
              as: 'team'
            }
          ],
          raw: false,
          nest: true
        });

        // If no player found with the exclusion filters, try again without the playerIdsAlreadyCycled filter
        if (!player && playerIdsAlreadyCycled.length > 0) {
          console.log(
            `🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ no player found for type ${type} with exclusion filters, trying without playerIdsAlreadyCycled filter`
          );

          // Remove the playerIdsAlreadyCycled filter but keep the addedPlayerIds filter
          const relaxedWhereClause = {
            status: 'not-bought',
            type_id: typeId
          };

          if (addedPlayerIds.size > 0) {
            relaxedWhereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }

          player = await Player.findOne({
            where: relaxedWhereClause,
            include: [
              {
                model: PlayerType,
                attributes: ['id', 'type'],
                as: 'player_type'
              },
              {
                model: Team,
                attributes: ['id', 'name'],
                as: 'team'
              }
            ],
            raw: false,
            nest: true
          });
        }

        if (player) {
          // Add this player ID to our tracking set
          addedPlayerIds.add(player.id);

          // Convert to plain object to avoid circular references
          const plainPlayer = {
            id: player.id,
            name: player.name,
            team_id: player.team_id,
            type_id: player.type_id,
            base_price: player.base_price,
            buy_price: player.buy_price,
            status: player.status,
            player_type: player.player_type
              ? {
                  id: player.player_type.id,
                  type: player.player_type.type
                }
              : null,
            team: player.team
              ? {
                  id: player.team.id,
                  name: player.team.name
                }
              : null
          };

          console.log(
            `🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ found player for type ${type}:`,
            plainPlayer.name
          );
          upcomingPlayers.push(plainPlayer);
        } else {
          console.log(`🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ no player found for type ${type}`);
        }
      }

      console.log('🚀🚀🚀 ~ Socket ~ getUpcomingPlayersForTypes ~ upcomingPlayers count:', upcomingPlayers.length);

      return upcomingPlayers;
    } catch (error) {
      console.error('Error fetching upcoming players for types:', error);
      return [];
    }
  }

  static async getUserDetails(socket) {
    try {
      // Get user details with transactions
      const userId = registeredUser[socket.id];
      const userDetails = await User.findByPk(userId, {
        include: [
          {
            model: Transaction,
            include: [
              {
                model: Player,
                include: [PlayerType, Team]
              }
            ]
          }
        ]
      });

      if (!userDetails) {
        throw new Error('User not found');
      }

      // Send response to client
      await this.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: 'getUserDetails',
          data: userDetails
        },
        'getUserDetails'
      );
    } catch (error) {
      console.error('Error in getUserDetails:', error);
      await this.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'Error',
          text: error.message || 'Failed to get user details'
        },
        'Error'
      );
    }
  }

  static async getAllUserDetails(io, socket, payload) {
    try {
      console.log('🚀🚀🚀 ~ Socket ~ getAllUserDetails ~ called with payload:', payload);

      // Validate the user and check if they're an admin
      const result = await Socket.validateUser(payload, true);

      if (!result || !result.user) {
        throw new Error('User validation failed');
      }

      // Get player type counts
      const playerTypeCounts = await Player.findAll({
        attributes: ['type_id', [sequelize.fn('COUNT', sequelize.col('player.id')), 'count']],
        include: [
          {
            model: PlayerType,
            attributes: ['type']
          }
        ],
        group: ['type_id', 'player_type.id', 'player_type.type'],
        raw: true
      });

      // Get all users with their transactions
      const allUserDetails = await User.findAll({
        where: {
          role: 2
        },
        include: [
          {
            model: Transaction,
            include: [
              {
                model: Player,
                include: [PlayerType, Team]
              }
            ]
          }
        ]
      });

      // Process player type counts
      const typeCountsObject = playerTypeCounts.reduce((acc, curr) => {
        const typeMapping = {
          BALL: 'totalBowler',
          WK: 'totalWK',
          BAT: 'totalBatsman'
        };

        const key = typeMapping[curr['player_type.type']] || curr['player_type.type'];
        acc[key] = parseInt(curr.count);
        return acc;
      }, {});

      // Get auction data
      const auctionData = await AuctionDetails.findOne({
        attributes: ['assigned_amount', 'max_batsman', 'max_bowler', 'max_wicketkeeper'],
        order: [['createdAt', 'DESC']]
      });

      const auctionSummary = auctionData ? auctionData.dataValues : {};
      const auctionInfo = {
        assigned_amount: auctionSummary.assigned_amount || 0,
        max_batsman: auctionSummary.max_batsman || 0,
        max_bowler: auctionSummary.max_bowler || 0,
        max_wicketkeeper: auctionSummary.max_wicketkeeper || 0
      };

      // Process user details
      const sanitizedUsers = await Promise.all(
        allUserDetails.map(async (user) => {
          const userObj = user.dataValues;
          delete userObj.password;
          userObj.token = payload.jwt;

          // Count total purchased players
          const totalPurchased = await Transaction.count({
            where: { buyer_id: user.id }
          });

          // Count purchased bowlers
          const bowlers = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [
                  {
                    model: PlayerType,
                    required: true,
                    where: { type: 'BALL' }
                  }
                ]
              }
            ],
            where: { buyer_id: user.id }
          });

          // Count purchased batsmen
          const batsmen = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [
                  {
                    model: PlayerType,
                    required: true,
                    where: { type: 'BAT' }
                  }
                ]
              }
            ],
            where: { buyer_id: user.id }
          });

          // Count purchased wicket keepers
          const wicketKeepers = await Transaction.count({
            include: [
              {
                model: Player,
                required: true,
                include: [
                  {
                    model: PlayerType,
                    required: true,
                    where: { type: 'WK' }
                  }
                ]
              }
            ],
            where: { buyer_id: user.id }
          });

          // Add purchase summary
          userObj.purchaseSummary = {
            totalPurchased,
            bowlers,
            batsmen,
            wicketKeepers
          };

          return userObj;
        })
      );

      // Send response to client
      await this.sendResponseToAdmins('getAllUserDetails', {
        data: {
          users: sanitizedUsers,
          playerTypeCounts: typeCountsObject,
          auctionInfo: auctionInfo
        }
      });
    } catch (error) {
      console.error('Error in getAllUserDetails:', error);
    }
  }
}
module.exports = Socket;
