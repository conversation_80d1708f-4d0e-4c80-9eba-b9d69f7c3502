# Socket.js Refactoring Analysis

## Current Structure Overview
The current `socket.js` file is 2831 lines long and contains all socket-related functionality in a single monolithic class. This analysis breaks down the functions by category for refactoring.

## Function Categories and Dependencies

### 1. **Utility Functions** (Lines 66-95, 142-164, 181-221)
- `modelToPlainObject()` - Converts Sequelize models to plain objects
- `getPlayersConfigResponse()` - Returns player configuration data
- `setPlayerTypesCycle()` - Sets up player type cycling
- `getPlayerMaxBidConfig()` - Gets user's max bid configuration
- `setMaxBidConfig()` - Sets max bid configuration
- `fireMaxBidEvent()` - Fires max bid reached event

**Dependencies**: Constants, global state variables
**Proposed Module**: `socketUtils.js`

### 2. **User Management Functions** (Lines 223-455)
- `validateUser()` - Validates JWT tokens and user permissions
- `checkAdminUser()` - Checks if user has admin role
- `registerUser()` - Registers user to socket session
- `disconnectClient()` - Handles user disconnection
- `isUserAdmin()` - Helper to check admin status

**Dependencies**: JWT, User model, global registeredUser object
**Proposed Module**: `userManager.js`

### 3. **Communication/Broadcasting Functions** (Lines 457-569, 2037-2206)
- `broadcastMessage()` - Broadcasts messages to all connected clients
- `sendResponseToClient()` - Sends response to specific client
- `sendResponseToAdmins()` - Sends messages to admin users only
- `getAndFireLiveUsersEvent()` - Gets and broadcasts live user data

**Dependencies**: Socket.io, User model, registeredUser object
**Proposed Module**: `communicationManager.js`

### 4. **Player Management Functions** (Lines 113-126, 597-835, 2447-2621)
- `getPlayerByPk()` - Gets player by primary key
- `updatePlayerAsUnsold()` - Marks player as unsold
- `updatePlayerAsSold()` - Marks player as sold and handles transactions
- `getUpcomingPlayers()` - Gets upcoming players in auction
- `getUpcomingPlayersForTypes()` - Gets upcoming players for specific types

**Dependencies**: Player, PlayerType, Team models, global state
**Proposed Module**: `playerManager.js`

### 5. **Auction Management Functions** (Lines 1230-1496, 2343-2446)
- `getPlayer()` - Gets next player for auction
- `getAnotherPlayer()` - Gets another player (admin function)
- `startAuction()` - Starts the auction process
- `setTimer()` - Manages auction timers
- `lastCall()` - Handles last call functionality

**Dependencies**: All other modules, global auction state
**Proposed Module**: `auctionManager.js`

### 6. **Bidding Management Functions** (Lines 1832-2036)
- `getBasePriceAndMaxBid()` - Gets base price and max bid for player
- `addBiddingRecord()` - Adds bidding record to database
- `checkCanUserBid()` - Validates if user can place bid
- `addBid()` - Processes bid placement

**Dependencies**: Bidding model, User model, player management
**Proposed Module**: `biddingManager.js`

### 7. **System/Status Functions** (Lines 1504-1831, 2208-2342)
- `reConnect()` - Handles user reconnection
- `auctionStatus()` - Gets current auction status
- `auctionCompleted()` - Handles auction completion
- `clearOlderData()` - Clears old auction data
- `handleUpcomingPlayersRequest()` - Handles upcoming players requests
- `getUserDetails()` - Gets individual user details
- `getAllUserDetails()` - Gets all user details

**Dependencies**: Various models, global state
**Proposed Module**: `systemManager.js`

## Global State Variables (Lines 17-95)
These need to be centralized in a state management module:
- `registeredUser` - Connected users mapping
- `countdownTimer` - Auction timer reference
- `duration`, `initialDuration` - Timer durations
- `auctionStartedTime` - Auction start timestamp
- `pauseActivated`, `isAuctionStarted`, `isLastCallActive` - Auction state flags
- `playerTypesCycleArr`, `playerTypesCycleIndex` - Player cycling state
- `playerIdsAlreadyCycled` - Cycled players tracking
- `maxBidConfig`, `playersBidStatus` - Bidding configuration
- `averagePlayerCost` - Average player cost
- `currentAuctionPlayerId` - Current player being auctioned

## Proposed Module Structure

```
server/util/socket/
├── index.js                 # Main socket controller
├── state/
│   └── auctionState.js     # Centralized state management
├── managers/
│   ├── userManager.js      # User management functions
│   ├── playerManager.js    # Player management functions
│   ├── auctionManager.js   # Auction management functions
│   ├── biddingManager.js   # Bidding management functions
│   ├── communicationManager.js # Broadcasting functions
│   └── systemManager.js    # System/status functions
└── utils/
    └── socketUtils.js      # Utility functions
```

## Dependencies Between Modules
1. **State Module** - Used by all other modules
2. **Utils Module** - Used by most other modules
3. **Communication Module** - Used by all manager modules
4. **User Module** - Used by auction, bidding, and system modules
5. **Player Module** - Used by auction and bidding modules
6. **Auction Module** - Uses all other modules
7. **Bidding Module** - Uses user, player, and communication modules
8. **System Module** - Uses user, player, and communication modules

## Refactoring Benefits
1. **Maintainability** - Smaller, focused files are easier to maintain
2. **Testability** - Individual modules can be unit tested
3. **Reusability** - Functions can be reused across different contexts
4. **Debugging** - Easier to locate and fix issues
5. **Team Development** - Multiple developers can work on different modules
6. **Code Organization** - Related functionality grouped together
