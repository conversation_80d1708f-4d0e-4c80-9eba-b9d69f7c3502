<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auction Start</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"], input[type="text"], input[type="tel"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #response, #tokenResponse, #verifyResponse {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
            word-break: break-all;
        }
        .websocket-status {
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 4px;
        }
        .copy-btn {
            background-color: #337ab7;
            margin-top: 10px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .player-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .player-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .player-type {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            margin-right: 5px;
            font-size: 12px;
            color: white;
        }
        .player-type-BAT {
            background-color: #007bff;
        }
        .player-type-BALL {
            background-color: #28a745;
        }
        .player-type-WK {
            background-color: #dc3545;
        }
        .player-details {
            margin-top: 5px;
            font-size: 14px;
        }
        .cycle-info {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }
        .cycle-type {
            display: inline-block;
            padding: 5px 10px;
            margin: 3px;
            border-radius: 3px;
            color: white;
        }
        .current-type {
            border: 2px solid #333;
        }
        .last-call-btn {
            background-color: #dc3545;
        }
        .last-call-btn:hover {
            background-color: #c82333;
        }
        .timer-display {
            text-align: center;
            margin: 20px 0;
        }
        #timerValue {
            font-size: 36px;
            font-weight: bold;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Test Auction Start</h1>
    
    <div id="wsStatus" class="websocket-status disconnected">
        WebSocket: Disconnected
    </div>

    <div class="section">
        <h2>1. Generate Admin Token</h2>
        <form id="tokenForm">
            <div class="form-group">
                <label for="phone">Phone Number:</label>
                <input type="tel" id="phone" name="phone" placeholder="Enter phone number" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" placeholder="Enter password" required>
            </div>
            
            <button type="submit">Generate Token</button>
        </form>
        
        <h3>Token:</h3>
        <pre id="tokenResponse">No token generated yet</pre>
        <div class="btn-group">
            <button id="copyToken" class="copy-btn">Copy Token</button>
            <button id="useToken">Use Token in Form Below</button>
            <button id="verifyToken" class="copy-btn">Verify Token</button>
        </div>
        
        <div id="verifyResponseContainer" style="display: none;">
            <h3>Token Verification:</h3>
            <pre id="verifyResponse"></pre>
        </div>
    </div>

    <div class="section">
        <h2>2. Start Auction</h2>
        <form id="auctionForm">
            <div class="form-group">
                <label for="jwt">JWT Token (Admin):</label>
                <input type="text" id="jwt" name="jwt" required>
            </div>
            
            <div class="form-group">
                <label for="duration">Duration (seconds):</label>
                <input type="number" id="duration" name="duration" value="30" required>
            </div>
            
            <div class="form-group">
                <label for="amount">Initial Amount:</label>
                <input type="number" id="amount" name="amount" value="1000" required>
            </div>
            
            
            <div class="form-group">
                <label for="batter">Batters Count:</label>
                <input type="number" id="batter" name="batter" value="2" required>
            </div>
            
            <div class="form-group">
                <label for="bowler">Bowlers Count:</label>
                <input type="number" id="bowler" name="bowler" value="2" required>
            </div>
            
            <div class="form-group">
                <label for="wicketKeeper">Wicket Keepers Count:</label>
                <input type="number" id="wicketKeeper" name="wicketKeeper" value="1" required>
            </div>
            
            <div class="form-group">
                <label for="batterMax">Max Batters:</label>
                <input type="number" id="batterMax" name="batterMax" value="3" required>
            </div>
            
            <div class="form-group">
                <label for="bowlerMax">Max Bowlers:</label>
                <input type="number" id="bowlerMax" name="bowlerMax" value="3" required>
            </div>
            
            <div class="form-group">
                <label for="wicketKeeperMax">Max Wicket Keepers:</label>
                <input type="number" id="wicketKeeperMax" name="wicketKeeperMax" value="2" required>
            </div>
            
            <div class="form-group">
                <label for="averagePlayerCost">Average Player Cost:</label>
                <input type="number" id="averagePlayerCost" name="averagePlayerCost" value="500" required>
            </div>
            
            <button type="submit">Start Auction</button>
        </form>
        
        <h3>Response:</h3>
        <pre id="response">No response yet</pre>
    </div>
    
    <div class="section">
        <h2>3. Get Upcoming Players</h2>
        <p>Retrieve the list of players that are available for upcoming auctions based on the player type cycle (excluding the current player).</p>
        
        <div class="form-group">
            <label for="upcomingJwt">JWT Token (Optional):</label>
            <input type="text" id="upcomingJwt" name="upcomingJwt">
            <small>Token is optional for this request. If provided, user will be validated.</small>
        </div>
        
        <div class="btn-group">
            <button id="getUpcomingBtn" class="copy-btn">Get Upcoming Players</button>
            <button id="useTokenForUpcoming">Use Token from Above</button>
        </div>
        
        <h3>Response:</h3>
        <pre id="upcomingResponse">No response yet</pre>

        <div id="upcomingPlayersInfo" style="display: none;">
            <h3>Cycle Information:</h3>
            <div id="cycleInfo"></div>

            <h3>Upcoming Players:</h3>
            <div id="playersList"></div>
        </div>
    </div>

    <div class="section">
        <h2>4. Get Live Bidders</h2>
        <p>Retrieve the list of participating bidders with their balances and purchase summaries (for users).</p>

        <div class="form-group">
            <label for="liveBiddersJwt">JWT Token (Required):</label>
            <input type="text" id="liveBiddersJwt" name="liveBiddersJwt">
            <small>Token is required for this request to validate the user.</small>
        </div>

        <div class="btn-group">
            <button id="getLiveBiddersBtn" class="copy-btn">Get Live Bidders</button>
            <button id="useTokenForLiveBidders">Use Token from Above</button>
        </div>

        <h3>Response:</h3>
        <pre id="liveBiddersResponse">No response yet</pre>
    </div>

    <div class="section">
        <h2>5. Last Call</h2>
        <p>Trigger a "Last Call" for the current player. This starts a timer, and if no bids are received before the timer expires, the player will be marked as unsold.</p>
        
        <div class="form-group">
            <label for="lastCallJwt">JWT Token (Admin):</label>
            <input type="text" id="lastCallJwt" name="lastCallJwt" required>
            <small>Admin token is required for this action.</small>
        </div>
        
        <div class="form-group">
            <label for="lastCallPlayerId">Player ID:</label>
            <input type="text" id="lastCallPlayerId" name="lastCallPlayerId" placeholder="Leave empty to use current player">
            <small>Optional. If left empty, the current player will be used.</small>
        </div>
        
        <div class="form-group">
            <label for="lastCallDuration">Duration (seconds):</label>
            <input type="number" id="lastCallDuration" name="lastCallDuration" value="30">
            <small>Duration of the Last Call timer in seconds.</small>
        </div>
        
        <div class="btn-group">
            <button id="lastCallBtn" class="last-call-btn">Trigger Last Call</button>
            <button id="useTokenForLastCall">Use Token from Above</button>
        </div>
        
        <h3>Response:</h3>
        <pre id="lastCallResponse">No response yet</pre>
        
        <div id="timerInfo" style="display: none;">
            <h3>Timer Information:</h3>
            <div class="timer-display">
                <div id="timerValue">30</div>
                <div>seconds remaining</div>
            </div>
        </div>
    </div>
    
    <div class="debug-info">
        <h3>Debug Information:</h3>
        <p>WebSocket ID: <span id="wsId">Not connected</span></p>
        <p>Last Message Sent: <span id="lastSent">None</span></p>
        <p>Last Message Received: <span id="lastReceived">None</span></p>
    </div>

    <script>
        let socket;
        const wsStatus = document.getElementById('wsStatus');
        const responseElement = document.getElementById('response');
        const tokenResponseElement = document.getElementById('tokenResponse');
        const verifyResponseElement = document.getElementById('verifyResponse');
        const verifyResponseContainer = document.getElementById('verifyResponseContainer');
        const upcomingResponseElement = document.getElementById('upcomingResponse');
        const upcomingPlayersInfoElement = document.getElementById('upcomingPlayersInfo');
        const cycleInfoElement = document.getElementById('cycleInfo');
        const playersListElement = document.getElementById('playersList');
        const auctionForm = document.getElementById('auctionForm');
        const tokenForm = document.getElementById('tokenForm');
        const copyTokenBtn = document.getElementById('copyToken');
        const useTokenBtn = document.getElementById('useToken');
        const verifyTokenBtn = document.getElementById('verifyToken');
        const getUpcomingBtn = document.getElementById('getUpcomingBtn');
        const useTokenForUpcomingBtn = document.getElementById('useTokenForUpcoming');
        const getLiveBiddersBtn = document.getElementById('getLiveBiddersBtn');
        const useTokenForLiveBiddersBtn = document.getElementById('useTokenForLiveBidders');
        const lastCallBtn = document.getElementById('lastCallBtn');
        const useTokenForLastCallBtn = document.getElementById('useTokenForLastCall');
        const lastCallResponseElement = document.getElementById('lastCallResponse');
        const timerInfoElement = document.getElementById('timerInfo');
        const timerValueElement = document.getElementById('timerValue');
        const wsIdElement = document.getElementById('wsId');
        const liveBiddersResponseElement = document.getElementById('liveBiddersResponse');
        const lastSentElement = document.getElementById('lastSent');
        const lastReceivedElement = document.getElementById('lastReceived');
        
        // Token generation form
        tokenForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/test/generate-admin-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone, password }),
                });
                
                const data = await response.json();
                
                if (data.token) {
                    tokenResponseElement.textContent = data.token;
                    tokenResponseElement.classList.add('success');
                    tokenResponseElement.classList.remove('error');
                } else {
                    tokenResponseElement.textContent = 'Error: ' + (data.message || 'Failed to generate token');
                    tokenResponseElement.classList.add('error');
                    tokenResponseElement.classList.remove('success');
                }
            } catch (error) {
                tokenResponseElement.textContent = 'Error: ' + error.message;
                tokenResponseElement.classList.add('error');
                tokenResponseElement.classList.remove('success');
            }
        });
        
        // Get Upcoming Players button
        getUpcomingBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('WebSocket is not connected. Please wait for connection.');
                return;
            }
            
            const jwt = document.getElementById('upcomingJwt').value;
            const payload = {
                action: 'getUpcomingPlayers'
            };
            
            if (jwt) {
                payload.jwt = jwt;
            }
            
            const payloadStr = JSON.stringify(payload);
            socket.send(payloadStr);
            lastSentElement.textContent = payloadStr;
            console.log('Sent getUpcomingPlayers request:', payload);
        });
        
        // Verify token button
        verifyTokenBtn.addEventListener('click', async function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                try {
                    const response = await fetch('/test/check-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ token }),
                    });
                    
                    const data = await response.json();
                    verifyResponseContainer.style.display = 'block';
                    
                    if (data.valid) {
                        verifyResponseElement.textContent = JSON.stringify(data, null, 2);
                        verifyResponseElement.classList.add('success');
                        verifyResponseElement.classList.remove('error');
                    } else {
                        verifyResponseElement.textContent = 'Error: ' + (data.message || 'Invalid token');
                        verifyResponseElement.classList.add('error');
                        verifyResponseElement.classList.remove('success');
                    }
                } catch (error) {
                    verifyResponseContainer.style.display = 'block';
                    verifyResponseElement.textContent = 'Error: ' + error.message;
                    verifyResponseElement.classList.add('error');
                    verifyResponseElement.classList.remove('success');
                }
            }
        });
        
        // Copy token button
        copyTokenBtn.addEventListener('click', function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                navigator.clipboard.writeText(token)
                    .then(() => {
                        const originalText = copyTokenBtn.textContent;
                        copyTokenBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyTokenBtn.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy: ', err);
                    });
            }
        });
        
        // Use token button
        useTokenBtn.addEventListener('click', function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                document.getElementById('jwt').value = token;
            }
        });
        
        // Use token for upcoming players button
        useTokenForUpcomingBtn.addEventListener('click', function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                document.getElementById('upcomingJwt').value = token;
            }
        });

        // Get Live Bidders button
        getLiveBiddersBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('WebSocket is not connected. Please wait for connection.');
                return;
            }

            const jwt = document.getElementById('liveBiddersJwt').value;
            if (!jwt) {
                alert('JWT Token is required for Live Bidders');
                return;
            }

            const payload = {
                action: 'liveBidders',
                jwt: jwt
            };

            const payloadStr = JSON.stringify(payload);
            socket.send(payloadStr);
            lastSentElement.textContent = payloadStr;
            console.log('Sent liveBidders request:', payload);
        });

        // Use token for live bidders button
        useTokenForLiveBiddersBtn.addEventListener('click', function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                document.getElementById('liveBiddersJwt').value = token;
            }
        });

        // Last Call button
        lastCallBtn.addEventListener('click', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('WebSocket is not connected. Please wait for connection.');
                return;
            }
            
            const jwt = document.getElementById('lastCallJwt').value;
            if (!jwt) {
                alert('JWT Token is required for Last Call');
                return;
            }
            
            let playerId = document.getElementById('lastCallPlayerId').value;
            const duration = parseInt(document.getElementById('lastCallDuration').value) || 30;
            
            // If no player ID is provided, try to extract it from the current player data
            if (!playerId) {
                try {
                    // Try to get the current player ID from the response element
                    const responseData = JSON.parse(responseElement.textContent);
                    if (responseData && responseData.text && responseData.text.player && responseData.text.player.id) {
                        playerId = responseData.text.player.id;
                        console.log('Using current player ID from response:', playerId);
                    }
                } catch (error) {
                    console.error('Failed to parse response data:', error);
                }
            }
            
            const payload = {
                action: 'lastCall',
                jwt: jwt,
                duration: duration
            };
            
            if (playerId) {
                payload.playerId = playerId;
                console.log('Using player ID:', playerId);
            } else {
                console.log('No player ID provided, server will try to find the current player');
            }
            
            const payloadStr = JSON.stringify(payload);
            socket.send(payloadStr);
            lastSentElement.textContent = payloadStr;
            console.log('Sent Last Call request:', payload);
        });
        
        // Use token for Last Call button
        useTokenForLastCallBtn.addEventListener('click', function() {
            const token = tokenResponseElement.textContent;
            if (token && token !== 'No token generated yet') {
                document.getElementById('lastCallJwt').value = token;
            }
        });
        
        // Timer countdown function
        let timerInterval;
        function startTimer(duration) {
            clearInterval(timerInterval);
            timerInfoElement.style.display = 'block';
            timerValueElement.textContent = duration;
            
            timerInterval = setInterval(function() {
                let seconds = parseInt(timerValueElement.textContent);
                if (seconds > 0) {
                    seconds--;
                    timerValueElement.textContent = seconds;
                } else {
                    clearInterval(timerInterval);
                }
            }, 1000);
        }
        
        // Function to stop timer
        function stopTimer() {
            clearInterval(timerInterval);
            timerInfoElement.style.display = 'none';
        }

        // Connect to WebSocket
        function connectWebSocket() {
            // Get the current host and protocol
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function() {
                wsStatus.textContent = 'WebSocket: Connected';
                wsStatus.classList.remove('disconnected');
                wsStatus.classList.add('connected');
                console.log('WebSocket connected');
            };
            
            socket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    lastReceivedElement.textContent = JSON.stringify(data);
                    console.log('Received:', data);
                    
                    // Update WebSocket ID if available
                    if (data.socketId) {
                        wsIdElement.textContent = data.socketId;
                    }
                    
                    // Handle error responses
                    if (data.action === 'error') {
                        // Display error message in appropriate response element based on context
                        if (data.message && data.message.includes('Last Call')) {
                            lastCallResponseElement.textContent = JSON.stringify(data, null, 2);
                            lastCallResponseElement.classList.add('error');
                            alert('Error: ' + data.message);
                        } else {
                            responseElement.textContent = JSON.stringify(data, null, 2);
                            responseElement.classList.add('error');
                        }
                        return;
                    }
                    
                    // Route the response to the appropriate element based on the action
                    if (data.action === 'getUpcomingPlayers') {
                        upcomingResponseElement.textContent = JSON.stringify(data, null, 2);

                        // Display upcoming players in a more user-friendly way
                        if (data.status && data.data && data.data.upcomingPlayers) {
                            displayUpcomingPlayers(data.data);
                        }
                    } else if (data.action === 'liveBidders') {
                        liveBiddersResponseElement.textContent = JSON.stringify(data, null, 2);
                    } else if (data.action === 'startAuction') {
                        responseElement.textContent = JSON.stringify(data, null, 2);
                    } else if (data.action === 'lastCall') {
                        lastCallResponseElement.textContent = JSON.stringify(data, null, 2);
                        
                        // Start the timer if Last Call is active
                        if (data.text && data.text.lastCallActive && data.text.timerDuration) {
                            startTimer(data.text.timerDuration);
                        }
                    } else if (data.action === 'lastCallCanceled') {
                        // When last call is canceled due to a new bid
                        stopTimer();
                        lastCallResponseElement.textContent = JSON.stringify(data, null, 2);
                        alert('Last call has been canceled due to a new bid. Please trigger last call again when ready.');
                    } else if (data.action === 'resetLastCallTimer') {
                        // If a new bid is received, stop the timer
                        if (data.text && data.text.lastCallActive === false) {
                            stopTimer();
                        }
                        responseElement.textContent = JSON.stringify(data, null, 2);
                    } else if (data.action === 'Player unsold' || data.action === 'Sold Player') {
                        // When a player is marked as unsold or sold, stop the timer
                        if (data.text && data.text.lastCallCompleted) {
                            stopTimer();
                        }
                        responseElement.textContent = JSON.stringify(data, null, 2);
                    } else {
                        // Default to the main response element for other actions
                        responseElement.textContent = JSON.stringify(data, null, 2);
                    }
                } catch (e) {
                    responseElement.textContent = event.data;
                    lastReceivedElement.textContent = event.data;
                    console.log('Received (non-JSON):', event.data);
                }
            };
            
            socket.onclose = function() {
                wsStatus.textContent = 'WebSocket: Disconnected';
                wsStatus.classList.remove('connected');
                wsStatus.classList.add('disconnected');
                wsIdElement.textContent = 'Not connected';
                console.log('WebSocket disconnected');
                // Try to reconnect after 5 seconds
                setTimeout(connectWebSocket, 5000);
            };
            
            socket.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        // Handle auction form submission
        auctionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('WebSocket is not connected. Please wait for connection.');
                return;
            }
            
            const formData = new FormData(auctionForm);
            const payload = {
                action: 'startAuction'
            };
            
            for (const [key, value] of formData.entries()) {
                if (key === 'jwt') {
                    payload[key] = value;
                } else {
                    payload[key] = Number(value);
                }
            }
            
            const payloadStr = JSON.stringify(payload);
            socket.send(payloadStr);
            lastSentElement.textContent = payloadStr;
            console.log('Sent:', payload);
        });

        // Connect when page loads
        connectWebSocket();
        
        // Function to display upcoming players in a user-friendly way
        function displayUpcomingPlayers(data) {
            const { upcomingPlayers, cycle, debug } = data;
            
            // Show the container
            upcomingPlayersInfoElement.style.display = 'block';
            
            // Display cycle information
            let cycleHtml = '<div class="cycle-info">';
            cycleHtml += '<h4>Player Type Cycle:</h4>';
            
            if (cycle && cycle.playerTypesInCycle) {
                cycle.playerTypesInCycle.forEach((type, index) => {
                    const isCurrent = index === cycle.currentCycleIndex;
                    const typeClass = `cycle-type player-type-${type} ${isCurrent ? 'current-type' : ''}`;
                    cycleHtml += `<span class="${typeClass}">${type}${isCurrent ? ' (Current)' : ''}</span>`;
                });
            }
            
            cycleHtml += '<h4>Upcoming Types:</h4>';
            if (cycle && cycle.upcomingPlayerTypes) {
                cycle.upcomingPlayerTypes.forEach(type => {
                    cycleHtml += `<span class="cycle-type player-type-${type}">${type}</span>`;
                });
            }
            
            // Add player counts by type
            if (debug && debug.playerCountsByType) {
                cycleHtml += '<h4>Available Players by Type:</h4>';
                for (const type in debug.playerCountsByType) {
                    cycleHtml += `<div>${type}: ${debug.playerCountsByType[type]} players</div>`;
                }
            }
            
            cycleHtml += '</div>';
            cycleInfoElement.innerHTML = cycleHtml;
            
            // Display players
            let playersHtml = '';
            
            if (upcomingPlayers && upcomingPlayers.length > 0) {
                playersHtml += `<p>Showing ${upcomingPlayers.length} upcoming players in the cycle:</p>`;
                
                upcomingPlayers.forEach((player, index) => {
                    const playerType = player.player_type ? player.player_type.type : 'Unknown';
                    const teamName = player.team ? player.team.name : 'Unknown Team';
                    
                    playersHtml += `
                        <div class="player-card">
                            <div class="player-name">${index + 1}. ${player.name}</div>
                            <span class="player-type player-type-${playerType}">${playerType}</span>
                            <div class="player-details">
                                <div>Team: ${teamName}</div>
                                <div>Base Price: ${player.base_price}</div>
                                <div>Status: ${player.status}</div>
                            </div>
                        </div>
                    `;
                });
            } else {
                playersHtml = '<p>No upcoming players found.</p>';
            }
            
            playersListElement.innerHTML = playersHtml;
        }
    </script>
</body>
</html> 