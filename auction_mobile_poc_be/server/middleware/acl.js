const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
  const accessList = {
    1: [
      { method: 'POST', path: '/points/sync-player-points' },
      { method: 'POST', path: '/points/total-points' },
      { method: 'GET', path: '/user/export-csv' },
      { method: 'POST', path: '/user/import-csv' },
      { method: 'GET', path: '/user/all-user-details' },
      { method: 'POST', path: '/db/reset' }
    ],
    2: [
      { method: 'GET', path: '/user/details' },
      { method: 'GET', path: '/user/all-user-details' }
    ]
  };

  const role = res.locals.user.role;
  const isAllowed = _.find(accessList[role], { method: req.method, path: req.originalUrl.split('?')[0] });

  if (isAllowed) {
    next();
  } else {
    const responseObject = Utils.errorResponse();
    responseObject.message = res.__('ACCESS_DENIED');
    res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
    return;
  }
};
