/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-useless-catch */

const SocketController = require('../../util/socket');

class getAllDataService {
    /**
     * @desc This function is being used to get data of each event of socket when socket breaks or throws error
     * <AUTHOR>
     * @since 13/05/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getData (req) {
        let result;
        try {
            const { action, data } = req.body;
            if (!action) {
             console.log('Action is required');
            }

            switch (action) {
                case 'registerUser':
                    result = await SocketController.registerUser(null, null, data);
                    break;

                case 'disconnectUser':
                    result = await SocketController.disconnectClient(null, null, data);
                    break;

                case 'startAuction':
                    await SocketController.registerUser(null, null, data);
                    result = await SocketController.startAuction(null, null, data);
                    break;

                case 'addBid':
                    result = await SocketController.addBid(null, null, data);
                    break;

                case 'getNewPlayer':
                    try {
                        await SocketController.registerUser(null, null, data);
                    } catch (e) {
                        console.log('registerUser failed:', e.message);
                    }
                    result = await SocketController.getAnotherPlayer(null, null, data);
                    break;

                case 'reconnect':
                    result = await SocketController.reConnect(null, null, data);
                    break;

                case 'auctionStatus':
                    result = await SocketController.auctionStatus(null, null, data);
                    break;

                case 'auctionCompleted':
                    try {
                        await SocketController.registerUser(null, null, data);
                    } catch (e) {
                        console.log('registerUser failed:', e.message);
                    }
                    result = await SocketController.auctionCompleted(null, null, data);
                    break;

                case 'getUpcomingPlayers':
                    result = await SocketController.handleUpcomingPlayersRequest(null, null, data);
                    break;

                case 'lastCall':
                    try {
                        await SocketController.registerUser(null, null, data);
                    } catch (e) {
                        console.log('registerUser failed:', e.message);
                    }
                    result = await SocketController.lastCall(null, null, data);
                    break;

                case 'getUserDetails':
                    try {
                        await SocketController.registerUser(null, null, data);
                    } catch (e) {
                        console.log('registerUser failed:', e.message);
                    }
                    result = await SocketController.getUserDetails(null, null, data);
                    break;

                case 'getAllUserDetails':
                    try {
                        await SocketController.registerUser(null, null, data);
                    } catch (e) {
                        console.log('registerUser failed:', e.message);
                    }
                    result = await SocketController.getAllUserDetails(null, null, data);
                    break;

                default:
                    return console.log('Invalid action', action);
            }
            return result;
        } catch (error) {
            console.error('getData error:', error);
        }
        return result;
    }
}

module.exports = getAllDataService;
