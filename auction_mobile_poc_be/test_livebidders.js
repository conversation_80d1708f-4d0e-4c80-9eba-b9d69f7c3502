const io = require('socket.io-client');

// Test the liveBidders functionality
async function testLiveBidders() {
  console.log('🚀 Testing liveBidders functionality...');
  
  // Connect to the server
  const socket = io('http://localhost:3006');
  
  socket.on('connect', () => {
    console.log('✅ Connected to server with ID:', socket.id);
    
    // First, let's generate a token for a regular user
    console.log('📝 Generating token for regular user...');
    
    fetch('http://localhost:3006/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: '9876543218',
        password: 'Test@1234'
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('🔑 Token response:', data);
      
      if (data.status === 1 && data.data && data.data.token) {
        const token = data.data.token;
        console.log('✅ Token generated successfully');
        
        // Now test the liveBidders event
        console.log('📡 Sending liveBidders request...');
        
        const payload = {
          action: 'liveBidders',
          jwt: token
        };
        
        socket.emit('liveBidders', payload);
        console.log('📤 Sent liveBidders payload:', payload);
      } else {
        console.error('❌ Failed to generate token:', data);
        socket.disconnect();
      }
    })
    .catch(error => {
      console.error('❌ Error generating token:', error);
      socket.disconnect();
    });
  });
  
  socket.on('liveBidders', (data) => {
    console.log('📥 Received liveBidders response:', JSON.stringify(data, null, 2));
    
    // Check if the response has the expected structure
    if (data.status && data.users && data.playerTypeCounts && data.auctionInfo) {
      console.log('✅ liveBidders response has correct structure!');
      console.log('👥 Number of users:', data.users.length);
      console.log('🏏 Player type counts:', data.playerTypeCounts);
      console.log('💰 Auction info:', data.auctionInfo);
    } else {
      console.log('⚠️ liveBidders response structure might be different than expected');
    }
    
    socket.disconnect();
  });
  
  socket.on('error', (error) => {
    console.error('❌ Socket error:', error);
    socket.disconnect();
  });
  
  socket.on('disconnect', () => {
    console.log('🔌 Disconnected from server');
    process.exit(0);
  });
  
  // Timeout after 10 seconds
  setTimeout(() => {
    console.log('⏰ Test timeout - disconnecting');
    socket.disconnect();
  }, 10000);
}

testLiveBidders();
